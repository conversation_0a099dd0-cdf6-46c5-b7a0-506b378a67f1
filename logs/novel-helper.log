2025-07-04T11:21:24.600+08:00  INFO 15444 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 15444 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-07-04T11:21:24.603+08:00 DEBUG 15444 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-07-04T11:21:24.603+08:00  INFO 15444 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-07-04T11:21:27.411+08:00  INFO 15444 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tom<PERSON> initialized with port 8080 (http)
2025-07-04T11:21:27.421+08:00  INFO 15444 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-04T11:21:27.421+08:00  INFO 15444 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-04T11:21:27.461+08:00  INFO 15444 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-07-04T11:21:27.461+08:00  INFO 15444 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2829 ms
2025-07-04T11:21:27.755+08:00 DEBUG 15444 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-07-04T11:21:28.051+08:00  INFO 15444 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-07-04T11:21:28.064+08:00  INFO 15444 --- [Novel] [main] o.example.novel.config.WebClientConfig   : 为Gemini API配置代理: 127.0.0.1:7890
2025-07-04T11:21:28.223+08:00  INFO 15444 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 主任务线程池初始化完成 - 核心线程数: 20, 最大线程数: 80, 队列容量: 500
2025-07-04T11:21:28.298+08:00  INFO 15444 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 章节处理线程池初始化完成 - 核心线程数: 40, 最大线程数: 120, 队列容量: 1000
2025-07-04T11:21:28.303+08:00  INFO 15444 --- [Novel] [main] org.example.novel.config.AsyncConfig     : AI调用线程池初始化完成 - 核心线程数: 3, 最大线程数: 8, 队列容量: 200
2025-07-04T11:21:28.858+08:00  INFO 15444 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-07-04T11:21:28.865+08:00  INFO 15444 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 4.616 seconds (process running for 5.108)
2025-07-04T11:21:28.881+08:00  INFO 15444 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-04T11:21:28.998+08:00  INFO 15444 --- [Novel] [scheduling-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1ca876a4
2025-07-04T11:21:28.999+08:00  INFO 15444 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-04T11:21:29.114+08:00  INFO 15444 --- [Novel] [scheduling-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 清理过期任务完成，删除数量: 17
2025-07-04T11:21:42.886+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04T11:21:42.886+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-04T11:21:42.887+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-04T11:21:42.905+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:42.992+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.035+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.036+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.037+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.037+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.038+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.172+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.172+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.195+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-10] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:44.105+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-10] o.springdoc.api.AbstractOpenApiResource  : Init duration for springdoc-openapi is: 901 ms
2025-07-04T11:21:47.917+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:22:06.521+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:22:28.537+08:00 ERROR 15444 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : 无法获取JWT令牌中的用户名

io.jsonwebtoken.ExpiredJwtException: JWT expired at 2025-06-27T11:22:47Z. Current time: 2025-07-04T03:22:28Z, a difference of 575981534 milliseconds.  Allowed clock skew: 0 milliseconds.
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:427) ~[jjwt-impl-0.11.5.jar:0.11.5]
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529) ~[jjwt-impl-0.11.5.jar:0.11.5]
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589) ~[jjwt-impl-0.11.5.jar:0.11.5]
	at io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173) ~[jjwt-impl-0.11.5.jar:0.11.5]
	at org.example.novel.util.JwtUtil.getAllClaimsFromToken(JwtUtil.java:73) ~[classes/:na]
	at org.example.novel.util.JwtUtil.getClaimFromToken(JwtUtil.java:59) ~[classes/:na]
	at org.example.novel.util.JwtUtil.getUsernameFromToken(JwtUtil.java:37) ~[classes/:na]
	at org.example.novel.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:52) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243) ~[spring-webmvc-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238) ~[spring-security-config-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) ~[spring-web-6.2.7.jar:6.2.7]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]

