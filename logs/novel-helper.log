2025-07-04T11:21:24.600+08:00  INFO 15444 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 15444 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-07-04T11:21:24.603+08:00 DEBUG 15444 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-07-04T11:21:24.603+08:00  INFO 15444 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-07-04T11:21:27.411+08:00  INFO 15444 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tom<PERSON> initialized with port 8080 (http)
2025-07-04T11:21:27.421+08:00  INFO 15444 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-04T11:21:27.421+08:00  INFO 15444 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-04T11:21:27.461+08:00  INFO 15444 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-07-04T11:21:27.461+08:00  INFO 15444 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2829 ms
2025-07-04T11:21:27.755+08:00 DEBUG 15444 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-07-04T11:21:28.051+08:00  INFO 15444 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-07-04T11:21:28.064+08:00  INFO 15444 --- [Novel] [main] o.example.novel.config.WebClientConfig   : 为Gemini API配置代理: 127.0.0.1:7890
2025-07-04T11:21:28.223+08:00  INFO 15444 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 主任务线程池初始化完成 - 核心线程数: 20, 最大线程数: 80, 队列容量: 500
2025-07-04T11:21:28.298+08:00  INFO 15444 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 章节处理线程池初始化完成 - 核心线程数: 40, 最大线程数: 120, 队列容量: 1000
2025-07-04T11:21:28.303+08:00  INFO 15444 --- [Novel] [main] org.example.novel.config.AsyncConfig     : AI调用线程池初始化完成 - 核心线程数: 3, 最大线程数: 8, 队列容量: 200
2025-07-04T11:21:28.858+08:00  INFO 15444 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-07-04T11:21:28.865+08:00  INFO 15444 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 4.616 seconds (process running for 5.108)
2025-07-04T11:21:28.881+08:00  INFO 15444 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-04T11:21:28.998+08:00  INFO 15444 --- [Novel] [scheduling-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1ca876a4
2025-07-04T11:21:28.999+08:00  INFO 15444 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-04T11:21:29.114+08:00  INFO 15444 --- [Novel] [scheduling-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 清理过期任务完成，删除数量: 17
2025-07-04T11:21:42.886+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04T11:21:42.886+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-04T11:21:42.887+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-04T11:21:42.905+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:42.992+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.035+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.036+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.037+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.037+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.038+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.172+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.172+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:43.195+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-10] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:21:44.105+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-10] o.springdoc.api.AbstractOpenApiResource  : Init duration for springdoc-openapi is: 901 ms
2025-07-04T11:21:47.917+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:22:06.521+08:00  WARN 15444 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-07-04T11:22:28.537+08:00 ERROR 15444 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : 无法获取JWT令牌中的用户名

io.jsonwebtoken.ExpiredJwtException: JWT expired at 2025-06-27T11:22:47Z. Current time: 2025-07-04T03:22:28Z, a difference of 575981534 milliseconds.  Allowed clock skew: 0 milliseconds.
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:427) ~[jjwt-impl-0.11.5.jar:0.11.5]
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529) ~[jjwt-impl-0.11.5.jar:0.11.5]
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589) ~[jjwt-impl-0.11.5.jar:0.11.5]
	at io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173) ~[jjwt-impl-0.11.5.jar:0.11.5]
	at org.example.novel.util.JwtUtil.getAllClaimsFromToken(JwtUtil.java:73) ~[classes/:na]
	at org.example.novel.util.JwtUtil.getClaimFromToken(JwtUtil.java:59) ~[classes/:na]
	at org.example.novel.util.JwtUtil.getUsernameFromToken(JwtUtil.java:37) ~[classes/:na]
	at org.example.novel.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:52) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191) ~[spring-security-web-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243) ~[spring-webmvc-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238) ~[spring-security-config-6.4.6.jar:6.4.6]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) ~[spring-web-6.2.7.jar:6.2.7]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.2.7.jar:6.2.7]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.7.jar:6.2.7]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]

2025-07-04T11:33:03.426+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-2] o.e.n.s.impl.SceneParseServiceImpl       : 创建异步分镜解析任务: scene_parse_1751599983426_1, 主线程: http-nio-8080-exec-2
2025-07-04T11:33:03.427+08:00  INFO 15444 --- [Novel] [http-nio-8080-exec-2] o.e.n.controller.ChapterSceneController  : 异步分镜解析任务创建完成，taskId: scene_parse_1751599983426_1, 耗时: 1ms, 章节数: 1
2025-07-04T11:33:03.427+08:00  INFO 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始异步分镜解析任务: scene_parse_1751599983426_1, 异步线程: Novel-Main-1
2025-07-04T11:33:03.465+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 保存任务状态: scene_parse_1751599983426_1, 用户: 1, 状态: PENDING
2025-07-04T11:33:03.488+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 更新任务进度: scene_parse_1751599983426_1, 进度: 10%, 步骤: 验证小说权限
2025-07-04T11:33:03.535+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 更新任务进度: scene_parse_1751599983534_1, 进度: 20%, 步骤: 解析章节分镜: 第三章 六句真言
2025-07-04T11:33:03.535+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 解析章节分镜: 第三章 六句真言 (1)
2025-07-04T11:33:03.661+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.NovelChapterServiceImpl     : 从MinIO获取章节内容成功，UUID: 2f0fe075e57749f391cb776d16ef9dbc.txt, 内容长度: 3405
2025-07-04T11:33:03.668+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.NovelChapterServiceImpl     : 从MinIO获取章节内容成功，UUID: 2f0fe075e57749f391cb776d16ef9dbc.txt, 内容长度: 3405
2025-07-04T11:33:03.668+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始使用AI解析分镜，章节ID: 1, 原始内容长度: 3405
2025-07-04T11:33:03.668+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始简化小说内容，章节ID: 1, 原始长度: 3405
2025-07-04T11:33:03.669+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 3774
2025-07-04T11:33:03.669+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:33:33.694+08:00 ERROR 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.simplifyNovelContent(SceneParseServiceImpl.java:306) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:271) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 15 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:33:33.696+08:00  WARN 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第1次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:33:33.696+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 等待2000ms后重试
2025-07-04T11:33:35.697+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 3774
2025-07-04T11:33:35.697+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:34:05.698+08:00 ERROR 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.simplifyNovelContent(SceneParseServiceImpl.java:306) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:271) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 15 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:34:05.700+08:00  WARN 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第2次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:34:05.700+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 等待4000ms后重试
2025-07-04T11:34:09.702+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 3774
2025-07-04T11:34:09.702+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:34:39.703+08:00 ERROR 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.simplifyNovelContent(SceneParseServiceImpl.java:306) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:271) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 15 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:34:39.703+08:00  WARN 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第3次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:34:39.703+08:00  WARN 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 内容简化失败，章节ID: 1, 将使用原始内容: 重试失败，最后一次错误: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:34:39.703+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 内容简化完成，章节ID: 1, 简化后长度: 3405
2025-07-04T11:34:39.703+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 1548
2025-07-04T11:34:39.703+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:34:46.365+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini响应: 请提供您希望我处理的小说章节。我将严格按照您提供的所有规则，将其拆分为结构化的漫画分镜片段，并以指定的JSON格式输出。
2025-07-04T11:34:46.365+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : AI分镜解析响应: 请提供您希望我处理的小说章节。我将严格按照您提供的所有规则，将其拆分为结构化的漫画分镜片段，并以指定的JSON格式输出。
2025-07-04T11:34:46.365+08:00 DEBUG 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 章节分镜解析完成: 第三章 六句真言, 分镜数: 0
2025-07-04T11:34:46.410+08:00  INFO 15444 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 任务完成: scene_parse_1751599983426_1, 用户: 1, 耗时: 103366ms
2025-07-04T11:34:46.411+08:00  INFO 15444 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 异步分镜解析任务完成: scene_parse_1751599983426_1, 耗时: 102983ms, 异步线程: Novel-Main-1
2025-07-04T11:42:44.004+08:00  INFO 15444 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-04T11:42:44.007+08:00  INFO 15444 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-04T11:42:46.027+08:00  INFO 15444 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-04T11:42:46.031+08:00  INFO 15444 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-04T11:42:48.065+08:00  INFO 13176 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 13176 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-07-04T11:42:48.066+08:00 DEBUG 13176 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-07-04T11:42:48.066+08:00  INFO 13176 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-07-04T11:42:48.791+08:00  INFO 13176 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-04T11:42:48.798+08:00  INFO 13176 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-04T11:42:48.798+08:00  INFO 13176 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-04T11:42:48.829+08:00  INFO 13176 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-07-04T11:42:48.831+08:00  INFO 13176 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 730 ms
2025-07-04T11:42:49.016+08:00 DEBUG 13176 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-07-04T11:42:49.202+08:00  INFO 13176 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-07-04T11:42:49.211+08:00  INFO 13176 --- [Novel] [main] o.example.novel.config.WebClientConfig   : 为Gemini API配置代理: 127.0.0.1:7890
2025-07-04T11:42:49.299+08:00  INFO 13176 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 主任务线程池初始化完成 - 核心线程数: 20, 最大线程数: 80, 队列容量: 500
2025-07-04T11:42:49.351+08:00  INFO 13176 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 章节处理线程池初始化完成 - 核心线程数: 40, 最大线程数: 120, 队列容量: 1000
2025-07-04T11:42:49.354+08:00  INFO 13176 --- [Novel] [main] org.example.novel.config.AsyncConfig     : AI调用线程池初始化完成 - 核心线程数: 3, 最大线程数: 8, 队列容量: 200
2025-07-04T11:42:49.745+08:00  INFO 13176 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-07-04T11:42:49.750+08:00  INFO 13176 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 2.145 seconds (process running for 2.468)
2025-07-04T11:42:49.759+08:00  INFO 13176 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-04T11:42:49.840+08:00  INFO 13176 --- [Novel] [scheduling-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@397f04d6
2025-07-04T11:42:49.840+08:00  INFO 13176 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-04T11:42:49.859+08:00  INFO 13176 --- [Novel] [scheduling-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 清理过期任务完成，删除数量: 0
2025-07-04T11:42:54.360+08:00  INFO 13176 --- [Novel] [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04T11:42:54.361+08:00  INFO 13176 --- [Novel] [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-04T11:42:54.361+08:00  INFO 13176 --- [Novel] [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-07-04T11:42:54.470+08:00  INFO 13176 --- [Novel] [http-nio-8080-exec-2] o.e.n.s.impl.SceneParseServiceImpl       : 创建异步分镜解析任务: scene_parse_1751600574470_1, 主线程: http-nio-8080-exec-2
2025-07-04T11:42:54.471+08:00  INFO 13176 --- [Novel] [http-nio-8080-exec-2] o.e.n.controller.ChapterSceneController  : 异步分镜解析任务创建完成，taskId: scene_parse_1751600574470_1, 耗时: 1ms, 章节数: 1
2025-07-04T11:42:54.471+08:00  INFO 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始异步分镜解析任务: scene_parse_1751600574470_1, 异步线程: Novel-Main-1
2025-07-04T11:42:54.504+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 保存任务状态: scene_parse_1751600574470_1, 用户: 1, 状态: PENDING
2025-07-04T11:42:54.528+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 更新任务进度: scene_parse_1751600574470_1, 进度: 10%, 步骤: 验证小说权限
2025-07-04T11:42:54.536+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 更新任务进度: scene_parse_1751600574535_1, 进度: 20%, 步骤: 解析章节分镜: 第三章 六句真言
2025-07-04T11:42:54.536+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 解析章节分镜: 第三章 六句真言 (1)
2025-07-04T11:42:54.617+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.NovelChapterServiceImpl     : 从MinIO获取章节内容成功，UUID: 2f0fe075e57749f391cb776d16ef9dbc.txt, 内容长度: 3405
2025-07-04T11:42:54.625+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.NovelChapterServiceImpl     : 从MinIO获取章节内容成功，UUID: 2f0fe075e57749f391cb776d16ef9dbc.txt, 内容长度: 3405
2025-07-04T11:42:54.625+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始使用AI解析分镜，章节ID: 1, 原始内容长度: 3405
2025-07-04T11:42:54.625+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始简化小说内容，章节ID: 1, 原始长度: 3405
2025-07-04T11:42:54.626+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 3774
2025-07-04T11:42:54.626+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:43:24.666+08:00 ERROR 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.simplifyNovelContent(SceneParseServiceImpl.java:306) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:271) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 15 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:43:24.668+08:00  WARN 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第1次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:43:24.668+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 等待2000ms后重试
2025-07-04T11:43:26.669+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 3774
2025-07-04T11:43:26.670+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:43:56.672+08:00 ERROR 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.simplifyNovelContent(SceneParseServiceImpl.java:306) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:271) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 15 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:43:56.674+08:00  WARN 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第2次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:43:56.674+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 等待4000ms后重试
2025-07-04T11:44:00.676+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 3774
2025-07-04T11:44:00.677+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:44:30.680+08:00 ERROR 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.simplifyNovelContent(SceneParseServiceImpl.java:306) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:271) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 15 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:44:30.683+08:00  WARN 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第3次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:44:30.684+08:00  WARN 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 内容简化失败，章节ID: 1, 将使用原始内容: 重试失败，最后一次错误: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:44:30.685+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 内容简化完成，章节ID: 1, 简化后长度: 3405
2025-07-04T11:44:30.685+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 4995
2025-07-04T11:44:30.686+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:45:00.687+08:00 ERROR 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:279) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 14 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:45:00.689+08:00  WARN 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第1次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:45:00.690+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 等待2000ms后重试
2025-07-04T11:45:02.690+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 4995
2025-07-04T11:45:02.690+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:45:32.693+08:00 ERROR 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:279) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 14 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:45:32.694+08:00  WARN 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第2次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:45:32.695+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 等待4000ms后重试
2025-07-04T11:45:36.696+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 4995
2025-07-04T11:45:36.696+08:00 DEBUG 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:46:06.698+08:00 ERROR 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : Gemini API调用异常

reactor.core.Exceptions$ReactiveException: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.Exceptions.propagate(Exceptions.java:410) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:102) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.Mono.block(Mono.java:1779) ~[reactor-core-3.7.6.jar:3.7.6]
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:73) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:279) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104) ~[reactor-core-3.7.6.jar:3.7.6]
		... 14 common frames omitted
Caused by: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.7.6.jar:3.7.6]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.7.6.jar:3.7.6]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:309) ~[na:na]
	... 3 common frames omitted

2025-07-04T11:46:06.700+08:00  WARN 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 第3次尝试失败: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
2025-07-04T11:46:06.701+08:00 ERROR 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : AI分镜解析失败，章节ID: 1

org.example.novel.exception.BusinessException: 重试失败，最后一次错误: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:218) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.callGemini(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:279) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]
Caused by: org.example.novel.exception.BusinessException: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at org.example.novel.service.impl.AIClientServiceImpl.callGeminiInternal(AIClientServiceImpl.java:85) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.lambda$callGemini$0(AIClientServiceImpl.java:39) ~[classes/:na]
	at org.example.novel.service.impl.AIClientServiceImpl.executeWithRetry(AIClientServiceImpl.java:200) ~[classes/:na]
	... 10 common frames omitted

2025-07-04T11:46:06.702+08:00 ERROR 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 解析章节分镜失败，章节ID: 1

org.example.novel.exception.BusinessException: AI分镜解析失败: 重试失败，最后一次错误: Gemini API调用异常: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 30000ms in 'flatMap' (and no fallback has been configured)
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapterScene(SceneParseServiceImpl.java:286) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseSingleChapter(SceneParseServiceImpl.java:202) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.parseScene(SceneParseServiceImpl.java:158) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.executeSceneParseTask(SceneParseServiceImpl.java:91) ~[classes/:na]
	at org.example.novel.service.impl.SceneParseServiceImpl.lambda$parseSceneAsync$0(SceneParseServiceImpl.java:63) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1850) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1447) ~[na:na]

2025-07-04T11:46:06.804+08:00  INFO 13176 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 任务完成: scene_parse_1751600574470_1, 用户: 1, 耗时: 192706ms
2025-07-04T11:46:06.805+08:00  INFO 13176 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 异步分镜解析任务完成: scene_parse_1751600574470_1, 耗时: 192334ms, 异步线程: Novel-Main-1
2025-07-04T11:53:43.547+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Starting AIClientServiceTest using Java 24.0.1 with PID 15984 (started by 28968 in H:\code\program\Novel)
2025-07-04T11:53:43.548+08:00 DEBUG 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-07-04T11:53:43.549+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : No active profile set, falling back to 1 default profile: "default"
2025-07-04T11:53:45.490+08:00  INFO 15984 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-07-04T11:53:45.505+08:00  INFO 15984 --- [Novel] [main] o.example.novel.config.WebClientConfig   : 为Gemini API配置代理: 127.0.0.1:7890
2025-07-04T11:53:45.639+08:00  INFO 15984 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 主任务线程池初始化完成 - 核心线程数: 20, 最大线程数: 80, 队列容量: 500
2025-07-04T11:53:45.722+08:00  INFO 15984 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 章节处理线程池初始化完成 - 核心线程数: 40, 最大线程数: 120, 队列容量: 1000
2025-07-04T11:53:45.724+08:00  INFO 15984 --- [Novel] [main] org.example.novel.config.AsyncConfig     : AI调用线程池初始化完成 - 核心线程数: 3, 最大线程数: 8, 队列容量: 200
2025-07-04T11:53:46.395+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Started AIClientServiceTest in 3.091 seconds (process running for 4.17)
2025-07-04T11:53:46.408+08:00  INFO 15984 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-04T11:53:46.518+08:00  INFO 15984 --- [Novel] [scheduling-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3dd42f51
2025-07-04T11:53:46.519+08:00  INFO 15984 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-04T11:53:46.544+08:00  INFO 15984 --- [Novel] [scheduling-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 清理过期任务完成，删除数量: 0
2025-07-04T11:53:46.972+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : === AI客户端测试开始 ===
2025-07-04T11:53:46.973+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : 代理配置 - 启用: true, 地址: 127.0.0.1:7890
2025-07-04T11:53:46.973+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : 重试配置 - 最大次数: 1
2025-07-04T11:53:46.973+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : API配置 - 基础URL: https://gemini.liuyuao.xyz/v1beta, 模型: gemini-2.5-flash
2025-07-04T11:53:46.975+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : === 测试Gemini连接 ===
2025-07-04T11:53:46.975+08:00  INFO 15984 --- [Novel] [main] org.example.novel.util.ProxyTestUtil     : 测试代理连接，目标URL: https://generativelanguage.googleapis.com/v1beta/models?key=AIzaSyDxn9J3L9V0qAWYHFm9t8a095iy8MiUhs4
2025-07-04T11:53:48.261+08:00  INFO 15984 --- [Novel] [main] org.example.novel.util.ProxyTestUtil     : 代理连接测试成功
2025-07-04T11:53:48.261+08:00  INFO 15984 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Gemini连接测试结果: 成功
2025-07-04T11:53:48.276+08:00  INFO 15984 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-04T11:53:48.283+08:00  INFO 15984 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-04T11:54:25.517+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Starting AIClientServiceTest using Java 24.0.1 with PID 3524 (started by 28968 in H:\code\program\Novel)
2025-07-04T11:54:25.518+08:00 DEBUG 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-07-04T11:54:25.518+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : No active profile set, falling back to 1 default profile: "default"
2025-07-04T11:54:27.040+08:00  INFO 3524 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-07-04T11:54:27.051+08:00  INFO 3524 --- [Novel] [main] o.example.novel.config.WebClientConfig   : 为Gemini API配置代理: 127.0.0.1:7890
2025-07-04T11:54:27.180+08:00  INFO 3524 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 主任务线程池初始化完成 - 核心线程数: 20, 最大线程数: 80, 队列容量: 500
2025-07-04T11:54:27.269+08:00  INFO 3524 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 章节处理线程池初始化完成 - 核心线程数: 40, 最大线程数: 120, 队列容量: 1000
2025-07-04T11:54:27.271+08:00  INFO 3524 --- [Novel] [main] org.example.novel.config.AsyncConfig     : AI调用线程池初始化完成 - 核心线程数: 3, 最大线程数: 8, 队列容量: 200
2025-07-04T11:54:27.895+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Started AIClientServiceTest in 2.614 seconds (process running for 3.304)
2025-07-04T11:54:27.909+08:00  INFO 3524 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-04T11:54:28.021+08:00  INFO 3524 --- [Novel] [scheduling-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@604cff
2025-07-04T11:54:28.022+08:00  INFO 3524 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-04T11:54:28.053+08:00  INFO 3524 --- [Novel] [scheduling-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 清理过期任务完成，删除数量: 0
2025-07-04T11:54:28.425+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : === AI客户端测试开始 ===
2025-07-04T11:54:28.425+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : 代理配置 - 启用: true, 地址: 127.0.0.1:7890
2025-07-04T11:54:28.426+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : 重试配置 - 最大次数: 1
2025-07-04T11:54:28.426+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : API配置 - 基础URL: https://gemini.liuyuao.xyz/v1beta, 模型: gemini-2.5-flash
2025-07-04T11:54:28.427+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : === 测试简单Gemini API调用 ===
2025-07-04T11:54:28.427+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : 发送测试提示词: 请回答：1+1等于几？只需要回答数字。
2025-07-04T11:54:28.427+08:00 DEBUG 3524 --- [Novel] [main] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 19
2025-07-04T11:54:28.427+08:00 DEBUG 3524 --- [Novel] [main] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:54:30.194+08:00 DEBUG 3524 --- [Novel] [main] o.e.n.service.impl.AIClientServiceImpl   : Gemini响应: 2
2025-07-04T11:54:30.194+08:00  INFO 3524 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : API调用成功，响应: 2
2025-07-04T11:54:30.207+08:00  INFO 3524 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-04T11:54:30.210+08:00  INFO 3524 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-04T11:54:58.181+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Starting AIClientServiceTest using Java 24.0.1 with PID 28108 (started by 28968 in H:\code\program\Novel)
2025-07-04T11:54:58.181+08:00 DEBUG 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-07-04T11:54:58.182+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : No active profile set, falling back to 1 default profile: "default"
2025-07-04T11:54:59.660+08:00  INFO 28108 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-07-04T11:54:59.674+08:00  INFO 28108 --- [Novel] [main] o.example.novel.config.WebClientConfig   : 为Gemini API配置代理: 127.0.0.1:7890
2025-07-04T11:54:59.809+08:00  INFO 28108 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 主任务线程池初始化完成 - 核心线程数: 20, 最大线程数: 80, 队列容量: 500
2025-07-04T11:54:59.889+08:00  INFO 28108 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 章节处理线程池初始化完成 - 核心线程数: 40, 最大线程数: 120, 队列容量: 1000
2025-07-04T11:54:59.891+08:00  INFO 28108 --- [Novel] [main] org.example.novel.config.AsyncConfig     : AI调用线程池初始化完成 - 核心线程数: 3, 最大线程数: 8, 队列容量: 200
2025-07-04T11:55:00.525+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : Started AIClientServiceTest in 2.575 seconds (process running for 3.252)
2025-07-04T11:55:00.538+08:00  INFO 28108 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-04T11:55:00.636+08:00  INFO 28108 --- [Novel] [scheduling-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@c328726
2025-07-04T11:55:00.638+08:00  INFO 28108 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-04T11:55:00.671+08:00  INFO 28108 --- [Novel] [scheduling-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 清理过期任务完成，删除数量: 0
2025-07-04T11:55:01.039+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : === AI客户端测试开始 ===
2025-07-04T11:55:01.040+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : 代理配置 - 启用: true, 地址: 127.0.0.1:7890
2025-07-04T11:55:01.040+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : 重试配置 - 最大次数: 1
2025-07-04T11:55:01.040+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : API配置 - 基础URL: https://gemini.liuyuao.xyz/v1beta, 模型: gemini-2.5-flash
2025-07-04T11:55:01.041+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : === 测试简单Gemini API调用 ===
2025-07-04T11:55:01.041+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : 发送测试提示词: 请回答：1+1等于几？只需要回答数字。
2025-07-04T11:55:01.041+08:00 DEBUG 28108 --- [Novel] [main] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 19
2025-07-04T11:55:01.041+08:00 DEBUG 28108 --- [Novel] [main] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
2025-07-04T11:55:03.049+08:00 DEBUG 28108 --- [Novel] [main] o.e.n.service.impl.AIClientServiceImpl   : Gemini响应: 2
2025-07-04T11:55:03.049+08:00  INFO 28108 --- [Novel] [main] o.e.novel.service.AIClientServiceTest    : API调用成功，响应: 2
2025-07-04T11:55:03.064+08:00  INFO 28108 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-04T11:55:03.067+08:00  INFO 28108 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-04T11:55:12.727+08:00  INFO 13176 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-04T11:55:12.729+08:00  INFO 13176 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-04T11:55:14.752+08:00  INFO 13176 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-04T11:55:14.757+08:00  INFO 13176 --- [Novel] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-04T11:55:16.626+08:00  INFO 10972 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 10972 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-07-04T11:55:16.627+08:00 DEBUG 10972 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-07-04T11:55:16.628+08:00  INFO 10972 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-07-04T11:55:17.344+08:00  INFO 10972 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-04T11:55:17.352+08:00  INFO 10972 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-04T11:55:17.352+08:00  INFO 10972 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-04T11:55:17.379+08:00  INFO 10972 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-07-04T11:55:17.379+08:00  INFO 10972 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 722 ms
2025-07-04T11:55:17.566+08:00 DEBUG 10972 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-07-04T11:55:17.752+08:00  INFO 10972 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-07-04T11:55:17.761+08:00  INFO 10972 --- [Novel] [main] o.example.novel.config.WebClientConfig   : 为Gemini API配置代理: 127.0.0.1:7890
2025-07-04T11:55:17.850+08:00  INFO 10972 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 主任务线程池初始化完成 - 核心线程数: 20, 最大线程数: 80, 队列容量: 500
2025-07-04T11:55:17.903+08:00  INFO 10972 --- [Novel] [main] org.example.novel.config.AsyncConfig     : 章节处理线程池初始化完成 - 核心线程数: 40, 最大线程数: 120, 队列容量: 1000
2025-07-04T11:55:17.906+08:00  INFO 10972 --- [Novel] [main] org.example.novel.config.AsyncConfig     : AI调用线程池初始化完成 - 核心线程数: 3, 最大线程数: 8, 队列容量: 200
2025-07-04T11:55:18.304+08:00  INFO 10972 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-07-04T11:55:18.309+08:00  INFO 10972 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 1.969 seconds (process running for 2.312)
2025-07-04T11:55:18.319+08:00  INFO 10972 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-04T11:55:18.390+08:00  INFO 10972 --- [Novel] [scheduling-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@27dcdfa0
2025-07-04T11:55:18.391+08:00  INFO 10972 --- [Novel] [scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-04T11:55:18.410+08:00  INFO 10972 --- [Novel] [scheduling-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 清理过期任务完成，删除数量: 0
2025-07-04T11:55:25.389+08:00  INFO 10972 --- [Novel] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04T11:55:25.389+08:00  INFO 10972 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-04T11:55:25.390+08:00  INFO 10972 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-04T11:55:25.504+08:00  INFO 10972 --- [Novel] [http-nio-8080-exec-1] o.e.n.s.impl.SceneParseServiceImpl       : 创建异步分镜解析任务: scene_parse_1751601325503_1, 主线程: http-nio-8080-exec-1
2025-07-04T11:55:25.504+08:00  INFO 10972 --- [Novel] [http-nio-8080-exec-1] o.e.n.controller.ChapterSceneController  : 异步分镜解析任务创建完成，taskId: scene_parse_1751601325503_1, 耗时: 1ms, 章节数: 1
2025-07-04T11:55:25.504+08:00  INFO 10972 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始异步分镜解析任务: scene_parse_1751601325503_1, 异步线程: Novel-Main-1
2025-07-04T11:55:25.547+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 保存任务状态: scene_parse_1751601325503_1, 用户: 1, 状态: PENDING
2025-07-04T11:55:25.570+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 更新任务进度: scene_parse_1751601325503_1, 进度: 10%, 步骤: 验证小说权限
2025-07-04T11:55:25.762+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AsyncTaskServiceImpl  : 更新任务进度: scene_parse_1751601325760_1, 进度: 20%, 步骤: 解析章节分镜: 第三章 六句真言
2025-07-04T11:55:25.762+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 解析章节分镜: 第三章 六句真言 (1)
2025-07-04T11:55:25.850+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.s.impl.NovelChapterServiceImpl     : 从MinIO获取章节内容成功，UUID: 2f0fe075e57749f391cb776d16ef9dbc.txt, 内容长度: 3405
2025-07-04T11:55:25.857+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.s.impl.NovelChapterServiceImpl     : 从MinIO获取章节内容成功，UUID: 2f0fe075e57749f391cb776d16ef9dbc.txt, 内容长度: 3405
2025-07-04T11:55:25.857+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始使用AI解析分镜，章节ID: 1, 原始内容长度: 3405
2025-07-04T11:55:25.857+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.s.impl.SceneParseServiceImpl       : 开始简化小说内容，章节ID: 1, 原始长度: 3405
2025-07-04T11:55:25.858+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 调用Gemini API，提示词长度: 3774
2025-07-04T11:55:25.858+08:00 DEBUG 10972 --- [Novel] [Novel-Main-1] o.e.n.service.impl.AIClientServiceImpl   : 使用代理: 127.0.0.1:7890
