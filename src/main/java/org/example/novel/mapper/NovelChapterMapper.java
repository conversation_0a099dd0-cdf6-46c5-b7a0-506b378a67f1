package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.NovelChapter;

import java.util.List;

/**
 * 小说章节数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface NovelChapterMapper {

    /**
     * 通过ID查询章节
     *
     * @param id 章节ID
     * @return 章节信息
     */
    NovelChapter findById(@Param("id") Long id);

    /**
     * 通过小说ID查询章节列表
     *
     * @param novelId 小说ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 章节列表
     */
    List<NovelChapter> findByNovelId(@Param("novelId") Long novelId,
                                    @Param("offset") Integer offset,
                                    @Param("limit") Integer limit);

    /**
     * 通过小说ID和章节序号查询章节
     *
     * @param novelId 小说ID
     * @param chapterIndex 章节序号
     * @return 章节信息
     */
    NovelChapter findByNovelIdAndChapterIndex(@Param("novelId") Long novelId, 
                                             @Param("chapterIndex") Integer chapterIndex);

    /**
     * 统计小说的章节数量
     *
     * @param novelId 小说ID
     * @return 章节数量
     */
    Long countByNovelId(@Param("novelId") Long novelId);

    /**
     * 获取小说的最大章节序号
     *
     * @param novelId 小说ID
     * @return 最大章节序号
     */
    Integer getMaxChapterIndex(@Param("novelId") Long novelId);

    /**
     * 插入章节
     *
     * @param chapter 章节信息
     * @return 影响行数
     */
    int insert(NovelChapter chapter);

    /**
     * 批量插入章节
     *
     * @param chapters 章节列表
     * @return 影响行数
     */
    int batchInsert(@Param("chapters") List<NovelChapter> chapters);

    /**
     * 更新章节
     *
     * @param chapter 章节信息
     * @return 影响行数
     */
    int update(NovelChapter chapter);

    /**
     * 删除章节
     *
     * @param id 章节ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 删除小说的所有章节
     *
     * @param novelId 小说ID
     * @return 影响行数
     */
    int deleteByNovelId(@Param("novelId") Long novelId);

    /**
     * 检查章节是否属于指定小说
     *
     * @param id 章节ID
     * @param novelId 小说ID
     * @return 是否属于该小说
     */
    boolean existsByIdAndNovelId(@Param("id") Long id, @Param("novelId") Long novelId);

    /**
     * 更新章节的分镜解析状态
     *
     * @param id 章节ID
     * @param isSceneParsed 是否已分镜解析
     * @return 影响行数
     */
    int updateSceneParsedStatus(@Param("id") Long id, @Param("isSceneParsed") Boolean isSceneParsed);

    /**
     * 更新章节的角色解析状态
     *
     * @param id 章节ID
     * @param isCharacterParsed 是否已角色解析
     * @return 影响行数
     */
    int updateCharacterParsedStatus(@Param("id") Long id, @Param("isCharacterParsed") Boolean isCharacterParsed);
}
