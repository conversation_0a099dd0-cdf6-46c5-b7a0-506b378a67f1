package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.SceneImage;

import java.util.List;

/**
 * 分镜图片数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface SceneImageMapper {

    /**
     * 根据ID查询图片
     *
     * @param id 图片ID
     * @return 图片信息
     */
    SceneImage findById(@Param("id") Long id);

    /**
     * 根据分镜ID查询图片
     *
     * @param sceneId 分镜ID
     * @return 图片信息
     */
    SceneImage findBySceneId(@Param("sceneId") Long sceneId);

    /**
     * 根据章节ID查询所有图片
     *
     * @param chapterId 章节ID
     * @return 图片列表
     */
    List<SceneImage> findByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 根据状态查询图片列表
     *
     * @param status 状态
     * @param limit 限制数量
     * @return 图片列表
     */
    List<SceneImage> findByStatus(@Param("status") String status, @Param("limit") Integer limit);

    /**
     * 插入图片
     *
     * @param image 图片信息
     * @return 影响行数
     */
    int insert(SceneImage image);

    /**
     * 更新图片
     *
     * @param image 图片信息
     * @return 影响行数
     */
    int update(SceneImage image);

    /**
     * 更新图片状态
     *
     * @param id 图片ID
     * @param status 状态
     * @param errorMessage 错误信息
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("errorMessage") String errorMessage);

    /**
     * 删除图片
     *
     * @param id 图片ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据分镜ID删除图片
     *
     * @param sceneId 分镜ID
     * @return 影响行数
     */
    int deleteBySceneId(@Param("sceneId") Long sceneId);
}
