package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.SceneAudio;

import java.util.List;

/**
 * 分镜音频数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface SceneAudioMapper {

    /**
     * 根据ID查询音频
     *
     * @param id 音频ID
     * @return 音频信息
     */
    SceneAudio findById(@Param("id") Long id);

    /**
     * 根据分镜ID查询音频
     *
     * @param sceneId 分镜ID
     * @return 音频信息
     */
    SceneAudio findBySceneId(@Param("sceneId") Long sceneId);

    /**
     * 根据章节ID查询所有音频
     *
     * @param chapterId 章节ID
     * @return 音频列表
     */
    List<SceneAudio> findByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 根据状态查询音频列表
     *
     * @param status 状态
     * @param limit 限制数量
     * @return 音频列表
     */
    List<SceneAudio> findByStatus(@Param("status") String status, @Param("limit") Integer limit);

    /**
     * 插入音频
     *
     * @param audio 音频信息
     * @return 影响行数
     */
    int insert(SceneAudio audio);

    /**
     * 更新音频
     *
     * @param audio 音频信息
     * @return 影响行数
     */
    int update(SceneAudio audio);

    /**
     * 更新音频状态
     *
     * @param id 音频ID
     * @param status 状态
     * @param errorMessage 错误信息
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("errorMessage") String errorMessage);

    /**
     * 删除音频
     *
     * @param id 音频ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据分镜ID删除音频
     *
     * @param sceneId 分镜ID
     * @return 影响行数
     */
    int deleteBySceneId(@Param("sceneId") Long sceneId);
}
