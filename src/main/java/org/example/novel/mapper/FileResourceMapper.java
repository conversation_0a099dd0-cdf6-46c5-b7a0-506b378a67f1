package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.FileResource;

import java.util.List;

/**
 * 文件资源数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface FileResourceMapper {

    /**
     * 通过MD5查询文件资源
     *
     * @param md5 文件MD5值
     * @return 文件资源
     */
    FileResource findByMd5(@Param("md5") String md5);

    /**
     * 通过UUID查询文件资源
     *
     * @param uuid 文件UUID
     * @return 文件资源
     */
    FileResource findByUuid(@Param("uuid") String uuid);

    /**
     * 通过ID查询文件资源
     *
     * @param id 资源ID
     * @return 文件资源
     */
    FileResource findById(@Param("id") Long id);

    /**
     * 查询文件资源列表
     *
     * @param bucket 存储桶（可选）
     * @param mimeType MIME类型（可选）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 文件资源列表
     */
    List<FileResource> findList(@Param("bucket") String bucket, 
                               @Param("mimeType") String mimeType,
                               @Param("offset") Integer offset, 
                               @Param("limit") Integer limit);

    /**
     * 统计文件资源数量
     *
     * @param bucket 存储桶（可选）
     * @param mimeType MIME类型（可选）
     * @return 总数量
     */
    Long countList(@Param("bucket") String bucket, @Param("mimeType") String mimeType);

    /**
     * 插入文件资源
     *
     * @param fileResource 文件资源
     * @return 影响行数
     */
    int insert(FileResource fileResource);

    /**
     * 更新文件资源
     *
     * @param fileResource 文件资源
     * @return 影响行数
     */
    int update(FileResource fileResource);

    /**
     * 删除文件资源
     *
     * @param id 资源ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 通过UUID删除文件资源
     *
     * @param uuid 文件UUID
     * @return 影响行数
     */
    int deleteByUuid(@Param("uuid") String uuid);
}
