package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.Audio;

import java.util.List;

/**
 * 音频素材数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface AudioMapper {

    /**
     * 通过ID查询音频素材
     *
     * @param id 音频ID
     * @return 音频素材
     */
    Audio findById(@Param("id") Long id);

    /**
     * 通过用户ID查询音频素材列表
     *
     * @param userId 用户ID
     * @param name 音频名称（可选，模糊查询）
     * @param voiceType 音色类型（可选）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 音频素材列表
     */
    List<Audio> findByUserId(@Param("userId") Long userId,
                            @Param("name") String name,
                            @Param("voiceType") String voiceType,
                            @Param("offset") Integer offset,
                            @Param("limit") Integer limit);

    /**
     * 统计用户的音频素材数量
     *
     * @param userId 用户ID
     * @param name 音频名称（可选，模糊查询）
     * @param voiceType 音色类型（可选）
     * @return 总数量
     */
    Long countByUserId(@Param("userId") Long userId,
                      @Param("name") String name,
                      @Param("voiceType") String voiceType);

    /**
     * 查询所有音频素材列表（管理员用）
     *
     * @param name 音频名称（可选，模糊查询）
     * @param voiceType 音色类型（可选）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 音频素材列表
     */
    List<Audio> findAll(@Param("name") String name,
                       @Param("voiceType") String voiceType,
                       @Param("offset") Integer offset,
                       @Param("limit") Integer limit);

    /**
     * 统计所有音频素材数量（管理员用）
     *
     * @param name 音频名称（可选，模糊查询）
     * @param voiceType 音色类型（可选）
     * @return 总数量
     */
    Long countAll(@Param("name") String name, @Param("voiceType") String voiceType);

    /**
     * 插入音频素材
     *
     * @param audio 音频素材
     * @return 影响行数
     */
    int insert(Audio audio);

    /**
     * 更新音频素材
     *
     * @param audio 音频素材
     * @return 影响行数
     */
    int update(Audio audio);

    /**
     * 删除音频素材
     *
     * @param id 音频ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 检查音频是否属于指定用户
     *
     * @param id 音频ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    boolean existsByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);
}
