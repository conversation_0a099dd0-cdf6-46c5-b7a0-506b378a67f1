package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.Role;

import java.util.List;

/**
 * 角色素材数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface RoleMapper {

    /**
     * 通过ID查询角色素材
     *
     * @param id 角色ID
     * @return 角色素材
     */
    Role findById(@Param("id") Long id);

    /**
     * 通过用户ID查询角色素材列表
     *
     * @param userId 用户ID
     * @param name 角色名称（可选，模糊查询）
     * @param tags 标签（可选，模糊查询）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 角色素材列表
     */
    List<Role> findByUserId(@Param("userId") Long userId,
                           @Param("name") String name,
                           @Param("tags") String tags,
                           @Param("offset") Integer offset,
                           @Param("limit") Integer limit);

    /**
     * 统计用户的角色素材数量
     *
     * @param userId 用户ID
     * @param name 角色名称（可选，模糊查询）
     * @param tags 标签（可选，模糊查询）
     * @return 总数量
     */
    Long countByUserId(@Param("userId") Long userId,
                      @Param("name") String name,
                      @Param("tags") String tags);

    /**
     * 查询所有角色素材列表（管理员用）
     *
     * @param name 角色名称（可选，模糊查询）
     * @param tags 标签（可选，模糊查询）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 角色素材列表
     */
    List<Role> findAll(@Param("name") String name,
                      @Param("tags") String tags,
                      @Param("offset") Integer offset,
                      @Param("limit") Integer limit);

    /**
     * 统计所有角色素材数量（管理员用）
     *
     * @param name 角色名称（可选，模糊查询）
     * @param tags 标签（可选，模糊查询）
     * @return 总数量
     */
    Long countAll(@Param("name") String name, @Param("tags") String tags);

    /**
     * 插入角色素材
     *
     * @param role 角色素材
     * @return 影响行数
     */
    int insert(Role role);

    /**
     * 更新角色素材
     *
     * @param role 角色素材
     * @return 影响行数
     */
    int update(Role role);

    /**
     * 删除角色素材
     *
     * @param id 角色ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 检查角色是否属于指定用户
     *
     * @param id 角色ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    boolean existsByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);
}
