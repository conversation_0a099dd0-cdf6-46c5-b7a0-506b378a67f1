package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.ChapterScene;

import java.util.List;

/**
 * 章节分镜数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface ChapterSceneMapper {

    /**
     * 根据ID查询分镜
     *
     * @param id 分镜ID
     * @return 分镜信息
     */
    ChapterScene findById(@Param("id") Long id);

    /**
     * 根据章节ID查询分镜列表
     *
     * @param chapterId 章节ID
     * @return 分镜列表
     */
    List<ChapterScene> findByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 根据章节ID查询分镜数量
     *
     * @param chapterId 章节ID
     * @return 分镜数量
     */
    Long countByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 检查章节是否已有分镜
     *
     * @param chapterId 章节ID
     * @return 是否存在分镜
     */
    boolean existsByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 插入分镜
     *
     * @param scene 分镜信息
     * @return 影响行数
     */
    int insert(ChapterScene scene);

    /**
     * 批量插入分镜
     *
     * @param scenes 分镜列表
     * @return 影响行数
     */
    int batchInsert(@Param("scenes") List<ChapterScene> scenes);

    /**
     * 更新分镜
     *
     * @param scene 分镜信息
     * @return 影响行数
     */
    int update(ChapterScene scene);

    /**
     * 删除分镜
     *
     * @param id 分镜ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据章节ID删除所有分镜
     *
     * @param chapterId 章节ID
     * @return 影响行数
     */
    int deleteByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 更新分镜的角色关联
     *
     * @param id 分镜ID
     * @param characterId 角色ID
     * @return 影响行数
     */
    int updateCharacterLink(@Param("id") Long id, @Param("characterId") Long characterId);
}
