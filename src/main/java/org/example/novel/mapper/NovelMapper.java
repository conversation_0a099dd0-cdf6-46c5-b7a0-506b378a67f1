package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.Novel;

import java.util.List;

/**
 * 小说数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface NovelMapper {

    /**
     * 通过ID查询小说
     *
     * @param id 小说ID
     * @return 小说信息
     */
    Novel findById(@Param("id") Long id);

    /**
     * 通过用户ID查询小说列表
     *
     * @param userId 用户ID
     * @param title 小说标题（可选，模糊查询）
     * @param author 作者（可选，模糊查询）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 小说列表
     */
    List<Novel> findByUserId(@Param("userId") Long userId,
                            @Param("title") String title,
                            @Param("author") String author,
                            @Param("offset") Integer offset,
                            @Param("limit") Integer limit);

    /**
     * 统计用户的小说数量
     *
     * @param userId 用户ID
     * @param title 小说标题（可选，模糊查询）
     * @param author 作者（可选，模糊查询）
     * @return 总数量
     */
    Long countByUserId(@Param("userId") Long userId,
                      @Param("title") String title,
                      @Param("author") String author);

    /**
     * 查询所有小说列表（管理员用）
     *
     * @param title 小说标题（可选，模糊查询）
     * @param author 作者（可选，模糊查询）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 小说列表
     */
    List<Novel> findAll(@Param("title") String title,
                       @Param("author") String author,
                       @Param("offset") Integer offset,
                       @Param("limit") Integer limit);

    /**
     * 统计所有小说数量（管理员用）
     *
     * @param title 小说标题（可选，模糊查询）
     * @param author 作者（可选，模糊查询）
     * @return 总数量
     */
    Long countAll(@Param("title") String title, @Param("author") String author);

    /**
     * 插入小说
     *
     * @param novel 小说信息
     * @return 影响行数
     */
    int insert(Novel novel);

    /**
     * 更新小说
     *
     * @param novel 小说信息
     * @return 影响行数
     */
    int update(Novel novel);

    /**
     * 删除小说
     *
     * @param id 小说ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 检查小说是否属于指定用户
     *
     * @param id 小说ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    boolean existsByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 更新小说章节数量
     *
     * @param id 小说ID
     * @param chapterCount 章节数量
     * @return 影响行数
     */
    int updateChapterCount(@Param("id") Long id, @Param("chapterCount") Integer chapterCount);
}
