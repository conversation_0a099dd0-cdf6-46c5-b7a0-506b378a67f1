package org.example.novel.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.example.novel.entity.AsyncTask;
import org.example.novel.enums.TaskStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 异步任务数据访问层
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper
public interface AsyncTaskMapper {

    /**
     * 通过任务ID查询任务
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    AsyncTask findByTaskId(@Param("taskId") String taskId);

    /**
     * 通过任务ID和用户ID查询任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 任务信息
     */
    AsyncTask findByTaskIdAndUserId(@Param("taskId") String taskId, @Param("userId") Long userId);

    /**
     * 查询用户的任务列表
     *
     * @param userId 用户ID
     * @param taskType 任务类型（可选）
     * @param status 任务状态（可选）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 任务列表
     */
    List<AsyncTask> findByUserId(@Param("userId") Long userId,
                                @Param("taskType") String taskType,
                                @Param("status") TaskStatus status,
                                @Param("offset") Integer offset,
                                @Param("limit") Integer limit);

    /**
     * 统计用户的任务数量
     *
     * @param userId 用户ID
     * @param taskType 任务类型（可选）
     * @param status 任务状态（可选）
     * @return 任务数量
     */
    Long countByUserId(@Param("userId") Long userId,
                      @Param("taskType") String taskType,
                      @Param("status") TaskStatus status);

    /**
     * 插入任务
     *
     * @param task 任务信息
     * @return 影响行数
     */
    int insert(AsyncTask task);

    /**
     * 更新任务
     *
     * @param task 任务信息
     * @return 影响行数
     */
    int update(AsyncTask task);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @param progress 进度
     * @param currentStep 当前步骤
     * @return 影响行数
     */
    int updateProgress(@Param("taskId") String taskId,
                      @Param("status") TaskStatus status,
                      @Param("progress") Integer progress,
                      @Param("currentStep") String currentStep);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @param resultData 结果数据
     * @param errorMessage 错误信息
     * @param endTime 结束时间
     * @param duration 耗时
     * @return 影响行数
     */
    int completeTask(@Param("taskId") String taskId,
                    @Param("status") TaskStatus status,
                    @Param("resultData") String resultData,
                    @Param("errorMessage") String errorMessage,
                    @Param("endTime") LocalDateTime endTime,
                    @Param("duration") Long duration);

    /**
     * 删除过期任务
     *
     * @param expireTime 过期时间
     * @return 删除数量
     */
    int deleteExpiredTasks(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 查询运行中的任务
     *
     * @return 运行中的任务列表
     */
    List<AsyncTask> findRunningTasks();
}
