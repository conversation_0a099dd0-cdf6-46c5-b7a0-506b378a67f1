package org.example.novel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * AI配置属性类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Component
@ConfigurationProperties(prefix = "ai")
public class AIProperties {

    private Gemini gemini = new Gemini();
    private Ollama ollama = new Ollama();
    private String defaultProvider = "gemini";

    @Data
    public static class Gemini {
        private String apiKey;
        private String baseUrl = "https://generativelanguage.googleapis.com/v1beta";
        private String model = "gemini-2.0-flash";
        private Double temperature = 0.3;
        private Integer maxOutputTokens = 2048;
        private Duration timeout = Duration.ofSeconds(30);
        private Proxy proxy = new Proxy();
        private Retry retry = new Retry();
    }

    @Data
    public static class Ollama {
        private String baseUrl = "http://localhost:11434";
        private String model = "llama3.2";
        private Double temperature = 0.7;
        private Duration timeout = Duration.ofSeconds(60);
        private Retry retry = new Retry();
    }

    @Data
    public static class Proxy {
        private Boolean enabled = false;
        private String host = "127.0.0.1";
        private Integer port = 7890;
    }

    @Data
    public static class Retry {
        private Integer maxAttempts = 3;
        private Duration initialDelay = Duration.ofSeconds(2);
        private Double multiplier = 2.0;
        private Duration maxDelay = Duration.ofSeconds(30);
    }
}
