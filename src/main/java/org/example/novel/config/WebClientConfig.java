package org.example.novel.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;

import java.net.InetSocketAddress;

/**
 * WebClient配置类
 *
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebClientConfig {

    private final AIProperties aiProperties;

    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }

    @Bean
    public WebClient geminiWebClient() {
        WebClient.Builder builder = WebClient.builder();

        // 如果启用了代理，配置代理
        if (aiProperties.getGemini().getProxy().getEnabled()) {
            String proxyHost = aiProperties.getGemini().getProxy().getHost();
            Integer proxyPort = aiProperties.getGemini().getProxy().getPort();

            log.info("为Gemini API配置代理: {}:{}", proxyHost, proxyPort);

            HttpClient httpClient = HttpClient.create()
                    .proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP)
                            .address(new InetSocketAddress(proxyHost, proxyPort)));

            builder.clientConnector(new ReactorClientHttpConnector(httpClient));
        }

        return builder.build();
    }

    @Bean
    public WebClient ollamaWebClient() {
        // Ollama通常是本地服务，不需要代理
        return WebClient.builder().build();
    }
}
