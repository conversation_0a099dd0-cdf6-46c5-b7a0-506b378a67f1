package org.example.novel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Prompt配置属性类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Component
@ConfigurationProperties(prefix = "prompts")
public class PromptProperties {

    private String characterParse;
    private String sceneParse;
    private String simplifyPrompt;
    private String emotionAnalysis;
    private String simpleQa;
    private String textSummary;
    private String keywordExtraction;
}
