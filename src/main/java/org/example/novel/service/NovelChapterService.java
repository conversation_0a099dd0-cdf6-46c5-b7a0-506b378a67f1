package org.example.novel.service;

import org.example.novel.dto.BatchUploadChapterResponseDTO;
import org.example.novel.dto.NovelChapterDTO;
import org.example.novel.dto.UploadChapterRequestDTO;
import org.example.novel.util.ChapterExtractorUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 小说章节服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface NovelChapterService {

    /**
     * 从文本内容创建章节
     *
     * @param novelId 小说ID
     * @param content 文本内容
     * @param autoExtract 是否自动提取章节
     * @param userId 用户ID
     * @return 创建的章节列表
     */
    List<NovelChapterDTO> createChaptersFromContent(Long novelId, String content, Boolean autoExtract, Long userId);

    /**
     * 上传单个章节文件
     *
     * @param novelId 小说ID
     * @param file 章节文件
     * @param uploadRequest 上传请求
     * @param userId 用户ID
     * @return 章节DTO
     */
    NovelChapterDTO uploadChapterFile(Long novelId, MultipartFile file, UploadChapterRequestDTO uploadRequest, Long userId);

    /**
     * 批量上传章节文件
     *
     * @param novelId 小说ID
     * @param files 章节文件列表
     * @param overwrite 是否覆盖已存在的章节
     * @param userId 用户ID
     * @return 批量上传响应
     */
    BatchUploadChapterResponseDTO batchUploadChapterFiles(Long novelId, MultipartFile[] files, Boolean overwrite, Long userId);

    /**
     * 手动创建单个章节
     *
     * @param novelId 小说ID
     * @param title 章节标题
     * @param content 章节内容
     * @param userId 用户ID
     * @return 章节DTO
     */
    NovelChapterDTO createChapter(Long novelId, String title, String content, Long userId);

    /**
     * 更新章节
     *
     * @param chapterId 章节ID
     * @param title 章节标题
     * @param content 章节内容
     * @param userId 用户ID
     * @return 章节DTO
     */
    NovelChapterDTO updateChapter(Long chapterId, String title, String content, Long userId);

    /**
     * 删除章节
     *
     * @param chapterId 章节ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteChapter(Long chapterId, Long userId);

    /**
     * 获取章节详情（包含内容）
     *
     * @param chapterId 章节ID
     * @param userId 用户ID
     * @return 章节DTO
     */
    NovelChapterDTO getChapterById(Long chapterId, Long userId);

    /**
     * 获取小说的章节列表
     *
     * @param novelId 小说ID
     * @param userId 用户ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 章节列表
     */
    List<NovelChapterDTO> getNovelChapters(Long novelId, Long userId, Integer page, Integer size);

    /**
     * 统计小说的章节数量
     *
     * @param novelId 小说ID
     * @param userId 用户ID
     * @return 章节数量
     */
    Long countNovelChapters(Long novelId, Long userId);

    /**
     * 获取章节内容
     *
     * @param chapterId 章节ID
     * @param userId 用户ID
     * @return 章节内容
     */
    String getChapterContent(Long chapterId, Long userId);

    /**
     * 批量创建章节
     *
     * @param novelId 小说ID
     * @param chapters 章节信息列表
     * @param userId 用户ID
     * @return 创建的章节列表
     */
    List<NovelChapterDTO> batchCreateChapters(Long novelId, List<ChapterExtractorUtil.ChapterInfo> chapters, Long userId);

    /**
     * 更新章节的分镜解析状态
     *
     * @param chapterId 章节ID
     * @param isSceneParsed 是否已分镜解析
     * @param userId 用户ID
     * @return 是否更新成功
     */
    boolean updateSceneParsedStatus(Long chapterId, Boolean isSceneParsed, Long userId);

    /**
     * 更新章节的角色解析状态
     *
     * @param chapterId 章节ID
     * @param isCharacterParsed 是否已角色解析
     * @param userId 用户ID
     * @return 是否更新成功
     */
    boolean updateCharacterParsedStatus(Long chapterId, Boolean isCharacterParsed, Long userId);
}
