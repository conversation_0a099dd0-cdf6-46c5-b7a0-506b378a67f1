package org.example.novel.service.impl;

import org.example.novel.entity.User;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.UserMapper;
import org.example.novel.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public User findById(Long id) {
        return userMapper.findById(id);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    @Transactional
    public User register(User user) {
        // 检查用户名是否已存在
        if (userMapper.findByUsername(user.getUsername()) != null) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (user.getEmail() != null && userMapper.findByEmail(user.getEmail()) != null) {
            throw new BusinessException("邮箱已被注册");
        }

        // 设置默认角色
        if (user.getRole() == null) {
            user.setRole("USER");
        }

        // 密码加密
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // 插入用户
        userMapper.insert(user);
        return user;
    }

    @Override
    @Transactional
    public User update(User user) {
        // 检查用户是否存在
        User existingUser = userMapper.findById(user.getId());
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 如果修改了用户名，检查新用户名是否已存在
        if (!existingUser.getUsername().equals(user.getUsername()) && 
            userMapper.findByUsername(user.getUsername()) != null) {
            throw new BusinessException("用户名已存在");
        }

        // 如果修改了邮箱，检查新邮箱是否已存在
        if (user.getEmail() != null && !user.getEmail().equals(existingUser.getEmail()) && 
            userMapper.findByEmail(user.getEmail()) != null) {
            throw new BusinessException("邮箱已被注册");
        }

        // 如果修改了密码，进行加密
        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        } else {
            user.setPassword(existingUser.getPassword());
        }

        // 更新用户
        userMapper.update(user);
        return user;
    }

    @Override
    @Transactional
    public boolean deleteById(Long id) {
        return userMapper.deleteById(id) > 0;
    }
} 