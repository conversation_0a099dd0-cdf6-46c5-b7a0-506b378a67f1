package org.example.novel.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.entity.AsyncTask;
import org.example.novel.enums.TaskStatus;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.AsyncTaskMapper;
import org.example.novel.service.AsyncTaskService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 异步任务管理服务实现类
 * 使用数据库存储任务状态
 *
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
public class AsyncTaskServiceImpl implements AsyncTaskService {

    private final AsyncTaskMapper asyncTaskMapper;
    private final ObjectMapper objectMapper;

    // 显式构造函数（如果@RequiredArgsConstructor不工作）
    public AsyncTaskServiceImpl(AsyncTaskMapper asyncTaskMapper, ObjectMapper objectMapper) {
        this.asyncTaskMapper = asyncTaskMapper;
        this.objectMapper = objectMapper;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> AsyncTaskResultDTO<T> getTaskStatus(String taskId, Long userId) {
        AsyncTask task = asyncTaskMapper.findByTaskIdAndUserId(taskId, userId);
        if (task == null) {
            throw new BusinessException("任务不存在或无权访问: " + taskId);
        }

        return convertToDTO(task);
    }

    @Override
    public <T> void saveTaskStatus(AsyncTaskResultDTO<T> taskResult, Long userId) {
        try {
            AsyncTask task = AsyncTask.builder()
                    .taskId(taskResult.getTaskId())
                    .userId(userId)
                    .taskType(extractTaskType(taskResult.getDescription()))
                    .taskName(taskResult.getDescription())
                    .status(taskResult.getStatus())
                    .progress(taskResult.getProgress())
                    .currentStep(taskResult.getCurrentStep())
                    .requestData(null) // 可以后续扩展
                    .resultData(taskResult.getResult() != null ? objectMapper.writeValueAsString(taskResult.getResult()) : null)
                    .errorMessage(taskResult.getErrorMessage())
                    .startTime(taskResult.getStartTime())
                    .endTime(taskResult.getEndTime())
                    .duration(taskResult.getDuration())
                    .build();

            asyncTaskMapper.insert(task);
            log.debug("保存任务状态: {}, 用户: {}, 状态: {}", taskResult.getTaskId(), userId, taskResult.getStatus());
        } catch (JsonProcessingException e) {
            log.error("序列化任务结果失败", e);
            throw new BusinessException("保存任务状态失败");
        }
    }

    @Override
    public void updateTaskProgress(String taskId, Integer progress, String currentStep, Long userId) {
        asyncTaskMapper.updateProgress(taskId, TaskStatus.RUNNING, progress, currentStep);
        log.debug("更新任务进度: {}, 进度: {}%, 步骤: {}", taskId, progress, currentStep);
    }

    @Override
    public <T> void completeTask(String taskId, T result, Long userId) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            AsyncTask task = asyncTaskMapper.findByTaskIdAndUserId(taskId, userId);
            Long duration = null;
            if (task != null && task.getStartTime() != null) {
                duration = java.time.Duration.between(task.getStartTime(), endTime).toMillis();
            }

            String resultData = result != null ? objectMapper.writeValueAsString(result) : null;
            asyncTaskMapper.completeTask(taskId, TaskStatus.COMPLETED, resultData, null, endTime, duration);
            log.info("任务完成: {}, 用户: {}, 耗时: {}ms", taskId, userId, duration);
        } catch (JsonProcessingException e) {
            log.error("序列化任务结果失败", e);
            failTask(taskId, "序列化结果失败: " + e.getMessage(), userId);
        }
    }

    @Override
    public void failTask(String taskId, String errorMessage, Long userId) {
        LocalDateTime endTime = LocalDateTime.now();
        AsyncTask task = asyncTaskMapper.findByTaskIdAndUserId(taskId, userId);
        Long duration = null;
        if (task != null && task.getStartTime() != null) {
            duration = java.time.Duration.between(task.getStartTime(), endTime).toMillis();
        }

        asyncTaskMapper.completeTask(taskId, TaskStatus.FAILED, null, errorMessage, endTime, duration);
        log.warn("任务失败: {}, 用户: {}, 错误: {}", taskId, userId, errorMessage);
    }

    @Override
    public List<AsyncTask> getUserTasks(Long userId, String taskType, TaskStatus status, Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        int offset = (page - 1) * size;
        return asyncTaskMapper.findByUserId(userId, taskType, status, offset, size);
    }

    @Override
    public Long countUserTasks(Long userId, String taskType, TaskStatus status) {
        return asyncTaskMapper.countByUserId(userId, taskType, status);
    }

    /**
     * 定时清理过期任务（每小时执行一次）
     */
    @Override
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanupExpiredTasks() {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(7); // 7天过期
        int deletedCount = asyncTaskMapper.deleteExpiredTasks(expireTime);
        log.info("清理过期任务完成，删除数量: {}", deletedCount);
    }

    /**
     * 转换实体为DTO
     */
    @SuppressWarnings("unchecked")
    private <T> AsyncTaskResultDTO<T> convertToDTO(AsyncTask task) {
        try {
            Object result = null;
            if (task.getResultData() != null) {
                result = objectMapper.readValue(task.getResultData(), Object.class);
            }

            return AsyncTaskResultDTO.<T>builder()
                    .taskId(task.getTaskId())
                    .status(task.getStatus())
                    .description(task.getTaskName())
                    .progress(task.getProgress())
                    .currentStep(task.getCurrentStep())
                    .result((T) result)
                    .errorMessage(task.getErrorMessage())
                    .startTime(task.getStartTime())
                    .endTime(task.getEndTime())
                    .duration(task.getDuration())
                    .build();
        } catch (JsonProcessingException e) {
            log.error("反序列化任务结果失败", e);
            throw new BusinessException("获取任务状态失败");
        }
    }

    /**
     * 从任务描述中提取任务类型
     */
    private String extractTaskType(String description) {
        if (description == null) {
            return "UNKNOWN";
        }

        if (description.contains("角色解析")) {
            return "CHARACTER_PARSE";
        } else if (description.contains("场景解析")) {
            return "SCENE_PARSE";
        } else if (description.contains("视频生成")) {
            return "VIDEO_GENERATE";
        } else if (description.contains("图片生成")) {
            return "IMAGE_GENERATE";
        } else if (description.contains("音频生成")) {
            return "AUDIO_GENERATE";
        } else {
            return "OTHER";
        }
    }
}
