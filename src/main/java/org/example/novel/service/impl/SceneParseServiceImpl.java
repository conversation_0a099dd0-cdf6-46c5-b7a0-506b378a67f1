package org.example.novel.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.config.PromptProperties;
import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.dto.ChapterSceneDTO;
import org.example.novel.dto.SceneParseRequestDTO;
import org.example.novel.dto.SceneParseResponseDTO;
import org.example.novel.entity.ChapterScene;
import org.example.novel.entity.NovelChapter;
import org.example.novel.entity.NovelCharacter;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.ChapterSceneMapper;
import org.example.novel.mapper.NovelChapterMapper;
import org.example.novel.mapper.NovelCharacterMapper;
import org.example.novel.mapper.NovelMapper;
import org.example.novel.converter.ChapterSceneConverter;
import org.example.novel.service.AIClientService;
import org.example.novel.service.AsyncTaskService;
import org.example.novel.service.NovelChapterService;
import org.example.novel.service.SceneParseService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 分镜解析服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SceneParseServiceImpl implements SceneParseService {

    private final AIClientService aiClientService;
    private final PromptProperties promptProperties;
    private final AsyncTaskService asyncTaskService;
    private final NovelMapper novelMapper;
    private final NovelChapterMapper novelChapterMapper;
    private final ChapterSceneMapper chapterSceneMapper;
    private final NovelCharacterMapper novelCharacterMapper;
    private final NovelChapterService novelChapterService;
    private final ChapterSceneConverter chapterSceneConverter;
    private final Executor taskExecutor;
    private final ObjectMapper objectMapper;

    @Override
    public String parseSceneAsync(SceneParseRequestDTO parseRequest, Long userId) {
        // 生成任务ID（立即返回，不做任何耗时操作）
        String taskId = "scene_parse_" + System.currentTimeMillis() + "_" + userId;
        
        log.info("创建异步分镜解析任务: {}, 主线程: {}", taskId, Thread.currentThread().getName());
        
        // 使用CompletableFuture实现真正的异步执行
        CompletableFuture.runAsync(() -> {
            executeSceneParseTask(taskId, parseRequest, userId);
        }, taskExecutor);
        
        // 立即返回taskId
        return taskId;
    }

    /**
     * 异步执行分镜解析任务
     */
    private void executeSceneParseTask(String taskId, SceneParseRequestDTO parseRequest, Long userId) {
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            log.info("开始异步分镜解析任务: {}, 异步线程: {}", taskId, Thread.currentThread().getName());
            
            // 创建初始任务状态（在异步线程中）
            AsyncTaskResultDTO<SceneParseResponseDTO> taskResult = AsyncTaskResultDTO.pending(
                taskId, 
                "分镜解析任务 - 小说ID: " + parseRequest.getNovelId()
            );
            taskResult.setStartTime(startTime);
            asyncTaskService.saveTaskStatus(taskResult, userId);
            
            // 更新进度：验证权限
            asyncTaskService.updateTaskProgress(taskId, 10, "验证小说权限", userId);
            
            // 执行同步解析逻辑
            SceneParseResponseDTO result = parseScene(parseRequest, userId);
            
            // 标记任务完成
            asyncTaskService.completeTask(taskId, result, userId);
            log.info("异步分镜解析任务完成: {}, 耗时: {}ms, 异步线程: {}", taskId, 
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis(),
                    Thread.currentThread().getName());
            
        } catch (Exception e) {
            log.error("异步分镜解析任务失败: {}, 异步线程: {}", taskId, Thread.currentThread().getName(), e);
            
            // 如果任务状态还没保存，先保存一个失败状态
            try {
                AsyncTaskResultDTO<SceneParseResponseDTO> failedTask = AsyncTaskResultDTO.failed(
                    taskId, 
                    "分镜解析任务 - 小说ID: " + parseRequest.getNovelId(),
                    e.getMessage(),
                    startTime
                );
                asyncTaskService.saveTaskStatus(failedTask, userId);
            } catch (Exception saveException) {
                // 如果任务已存在，直接标记失败
                asyncTaskService.failTask(taskId, e.getMessage(), userId);
            }
        }
    }

    @Override
    @Deprecated
    public SceneParseResponseDTO parseScene(SceneParseRequestDTO parseRequest, Long userId) {
        // 验证小说所有权（在异步线程中执行，不影响taskId返回速度）
        validateNovelOwnership(parseRequest.getNovelId(), userId);
        
        // 批量验证章节所有权（一次查询，提高效率）
        validateChaptersOwnership(parseRequest.getNovelId(), parseRequest.getChapterIds(), userId);

        List<ChapterSceneDTO> allScenes = new ArrayList<>();
        List<SceneParseResponseDTO.FailedChapter> failedChapters = new ArrayList<>();
        int successCount = 0;

        int totalChapters = parseRequest.getChapterIds().size();
        int processedChapters = 0;
        
        for (Long chapterId : parseRequest.getChapterIds()) {
            try {
                // 更新进度
                int progress = 20 + (processedChapters * 70 / totalChapters);
                
                NovelChapter chapter = novelChapterMapper.findById(chapterId);
                String chapterTitle = chapter != null ? chapter.getTitle() : "章节" + chapterId;
                
                // 检查是否已有分镜
                if (!parseRequest.getOverwrite() && chapterSceneMapper.existsByChapterId(chapterId)) {
                    log.debug("章节已有分镜，跳过解析: {}", chapterTitle);
                    processedChapters++;
                    continue;
                }
                
                asyncTaskService.updateTaskProgress(
                    "scene_parse_" + System.currentTimeMillis() + "_" + userId, 
                    progress, 
                    "解析章节分镜: " + chapterTitle, 
                    userId
                );
                
                log.debug("解析章节分镜: {} ({})", chapterTitle, chapterId);
                
                List<ChapterSceneDTO> chapterScenes = parseSingleChapter(parseRequest.getNovelId(), chapterId, userId, parseRequest);
                allScenes.addAll(chapterScenes);
                successCount++;

                processedChapters++;
                log.debug("章节分镜解析完成: {}, 分镜数: {}", chapterTitle, chapterScenes.size());
                
            } catch (Exception e) {
                log.error("解析章节分镜失败，章节ID: {}", chapterId, e);
                
                NovelChapter chapter = novelChapterMapper.findById(chapterId);
                failedChapters.add(SceneParseResponseDTO.FailedChapter.builder()
                        .chapterId(chapterId)
                        .chapterTitle(chapter != null ? chapter.getTitle() : "未知章节")
                        .reason(e.getMessage())
                        .errorCode("PARSE_FAILED")
                        .build());
                        
                processedChapters++;
            }
        }

        return SceneParseResponseDTO.builder()
                .parsedScenes(allScenes)
                .failedChapters(failedChapters)
                .totalChapters(parseRequest.getChapterIds().size())
                .successCount(successCount)
                .failedCount(failedChapters.size())
                .totalScenes(allScenes.size())
                .build();
    }

    /**
     * 解析单个章节的分镜
     */
    private List<ChapterSceneDTO> parseSingleChapter(Long novelId, Long chapterId, Long userId, SceneParseRequestDTO parseRequest) {
        // 获取章节内容
        String content = novelChapterService.getChapterContent(chapterId, userId);
        
        if (!StringUtils.hasText(content)) {
            throw new BusinessException("章节内容为空");
        }

        // 调用AI解析分镜
        String scenesJson = parseSingleChapterScene(novelId, chapterId, userId);
        
        // 解析AI返回的JSON
        List<Map<String, Object>> sceneData = parseSceneJson(scenesJson);
        
        // 如果需要覆盖，先删除已有分镜
        if (parseRequest.getOverwrite()) {
            chapterSceneMapper.deleteByChapterId(chapterId);
        }
        
        // 创建分镜记录
        List<ChapterScene> scenes = new ArrayList<>();
        List<ChapterSceneDTO> sceneDTOs = new ArrayList<>();
        
        // 第一步：收集所有角色名称并去重
        Set<String> allCharacterNames = new HashSet<>();
        for (Map<String, Object> scene : sceneData) {
            @SuppressWarnings("unchecked")
            List<String> characterNames = (List<String>) scene.get("characters");
            if (characterNames != null) {
                allCharacterNames.addAll(characterNames);
            }
        }

        // 移除空值和旁白
        allCharacterNames.removeIf(name -> !StringUtils.hasText(name) || "Narrator".equals(name));

        log.debug("章节中发现的所有角色: {}", allCharacterNames);

        // 第二步：批量确保角色存在
        if (!allCharacterNames.isEmpty()) {
            ensureCharactersExistBatch(novelId, allCharacterNames, userId);
        }

        // 第三步：创建分镜数据
        for (int i = 0; i < sceneData.size(); i++) {
            Map<String, Object> scene = sceneData.get(i);

            String sceneContent = (String) scene.get("content");
            String speaker = (String) scene.get("speaker");
            String prompt = parseRequest.getGeneratePrompt() ? (String) scene.get("prompt") : null;

            // 处理角色列表
            @SuppressWarnings("unchecked")
            List<String> characterNames = (List<String>) scene.get("characters");
            if (characterNames == null) {
                characterNames = new ArrayList<>();
            }

            // 序列化角色列表为JSON
            String charactersJson;
            try {
                charactersJson = objectMapper.writeValueAsString(characterNames);
            } catch (Exception e) {
                log.warn("序列化角色列表失败: {}", characterNames, e);
                charactersJson = "[]";
            }

            ChapterScene chapterScene = ChapterScene.builder()
                    .chapterId(chapterId)
                    .sceneIndex(i + 1)
                    .content(sceneContent)
                    .speaker(speaker)
                    .prompt(prompt)
                    .characters(charactersJson)
                    .build();

            scenes.add(chapterScene);
        }
        
        // 批量插入分镜
        if (!scenes.isEmpty()) {
            chapterSceneMapper.batchInsert(scenes);
            
            // 转换为DTO
            for (ChapterScene scene : scenes) {
                sceneDTOs.add(chapterSceneConverter.toDTO(scene));
            }
        }
        
        return sceneDTOs;
    }

    @Override
    public String parseSingleChapterScene(Long novelId, Long chapterId, Long userId) {
        // 获取章节内容
        String content = novelChapterService.getChapterContent(chapterId, userId);

        if (!StringUtils.hasText(content)) {
            throw new BusinessException("章节内容为空");
        }

        try {
            log.debug("开始使用AI解析分镜，章节ID: {}, 原始内容长度: {}", chapterId, content.length());

            // 第一步：使用simplify-prompt简化小说内容（必须成功）
            String simplifiedContent = simplifyNovelContent(content, chapterId);
            log.debug("内容简化完成，章节ID: {}, 简化后长度: {}", chapterId, simplifiedContent.length());

            // 第二步：使用简化后的内容进行分镜解析
            String promptTemplate = promptProperties.getSceneParse();
            String fullPrompt = promptTemplate.replace("{content}", simplifiedContent);

            // 调用AI模型（使用Gemini进行分镜解析）
            String response = aiClientService.callGemini(fullPrompt);

            log.debug("AI分镜解析响应: {}", response);
            return response;

        } catch (BusinessException e) {
            // 重新抛出业务异常（包括简化失败的异常）
            throw e;
        } catch (Exception e) {
            log.error("AI分镜解析失败，章节ID: {}", chapterId, e);
            throw new BusinessException("AI分镜解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用AI简化小说内容
     *
     * @param content 原始章节内容
     * @param chapterId 章节ID（用于日志）
     * @return 简化后的内容
     * @throws BusinessException 如果简化失败
     */
    private String simplifyNovelContent(String content, Long chapterId) {
        try {
            log.debug("开始简化小说内容，章节ID: {}, 原始长度: {}", chapterId, content.length());

            // 构建简化提示词
            String simplifyTemplate = promptProperties.getSimplifyPrompt();
            String simplifyPrompt = simplifyTemplate.replace("{content}", content);

            // 调用AI模型进行内容简化
            String simplifiedContent = aiClientService.callGemini(simplifyPrompt);

            log.debug("内容简化完成，章节ID: {}, 简化前: {}字, 简化后: {}字",
                    chapterId, content.length(), simplifiedContent.length());

            return simplifiedContent;

        } catch (Exception e) {
            log.error("内容简化失败，章节ID: {}, 错误: {}", chapterId, e.getMessage());
            // 简化失败直接抛出异常，不再回退到原始内容
            throw new BusinessException("内容简化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析AI返回的分镜JSON
     */
    private List<Map<String, Object>> parseSceneJson(String scenesJson) {
        try {
            // 清理AI返回的文本，提取JSON部分（处理markdown格式）
            String cleanJson = cleanAIResponse(scenesJson);

            // 解析JSON
            return objectMapper.readValue(cleanJson, new TypeReference<List<Map<String, Object>>>() {});

        } catch (JsonProcessingException e) {
            log.error("解析分镜JSON失败: {}", scenesJson, e);
            throw new BusinessException("解析AI返回的分镜数据失败");
        }
    }

    /**
     * 清理AI响应，提取JSON部分（支持markdown格式）
     */
    private String cleanAIResponse(String response) {
        if (!StringUtils.hasText(response)) {
            return "[]";
        }

        log.debug("原始AI响应: {}", response);

        // 处理markdown格式的JSON代码块
        String cleanedResponse = response;

        // 移除markdown代码块标记
        if (response.contains("```json")) {
            int jsonStart = response.indexOf("```json") + 7;
            int jsonEnd = response.indexOf("```", jsonStart);
            if (jsonEnd != -1) {
                cleanedResponse = response.substring(jsonStart, jsonEnd).trim();
                log.debug("提取markdown中的JSON: {}", cleanedResponse);
            }
        } else if (response.contains("```")) {
            // 处理没有语言标识的代码块
            int jsonStart = response.indexOf("```") + 3;
            int jsonEnd = response.indexOf("```", jsonStart);
            if (jsonEnd != -1) {
                cleanedResponse = response.substring(jsonStart, jsonEnd).trim();
                log.debug("提取代码块中的JSON: {}", cleanedResponse);
            }
        }

        // 查找JSON数组的开始和结束
        int startIndex = cleanedResponse.indexOf('[');
        int endIndex = cleanedResponse.lastIndexOf(']');

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            String jsonResult = cleanedResponse.substring(startIndex, endIndex + 1);
            log.debug("最终提取的JSON: {}", jsonResult);
            return jsonResult;
        }

        log.warn("未能从AI响应中提取有效的JSON数组");
        // 如果没有找到JSON数组，返回空数组
        return "[]";
    }

    /**
     * 确保角色存在于数据库中，如果不存在则自动创建
     */
    private void ensureCharactersExist(Long novelId, List<String> characterNames, Long userId) {
        if (characterNames == null || characterNames.isEmpty()) {
            return;
        }

        for (String characterName : characterNames) {
            if (StringUtils.hasText(characterName) && !"Narrator".equals(characterName)) {
                // 检查角色是否已存在
                NovelCharacter existingCharacter = novelCharacterMapper.findByNovelIdAndName(novelId, characterName);

                if (existingCharacter == null) {
                    // 创建新角色
                    NovelCharacter newCharacter = NovelCharacter.builder()
                            .novelId(novelId)
                            .name(characterName)
                            .description("AI解析漏掉的角色")
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build();

                    novelCharacterMapper.insert(newCharacter);
                    log.info("自动创建角色: {} (小说ID: {})", characterName, novelId);
                }
            }
        }
    }

    /**
     * 验证小说所有权
     */
    private void validateNovelOwnership(Long novelId, Long userId) {
        if (!novelMapper.existsByIdAndUserId(novelId, userId)) {
            throw new BusinessException("无权访问该小说");
        }
    }

    /**
     * 批量验证章节所有权
     */
    private void validateChaptersOwnership(Long novelId, List<Long> chapterIds, Long userId) {
        for (Long chapterId : chapterIds) {
            NovelChapter chapter = novelChapterMapper.findById(chapterId);
            if (chapter == null) {
                throw new BusinessException("章节不存在: " + chapterId);
            }
            if (!chapter.getNovelId().equals(novelId)) {
                throw new BusinessException("章节不属于指定小说: " + chapterId);
            }
        }
    }
}
