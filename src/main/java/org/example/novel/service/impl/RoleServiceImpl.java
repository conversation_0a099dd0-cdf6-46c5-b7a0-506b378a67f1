package org.example.novel.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.converter.RoleConverter;
import org.example.novel.dto.CreateRoleRequestDTO;
import org.example.novel.dto.RoleDTO;
import org.example.novel.dto.UpdateRoleRequestDTO;
import org.example.novel.entity.Role;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.RoleMapper;
import org.example.novel.service.FileResourceService;
import org.example.novel.service.RoleService;
import org.example.novel.util.MinioUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色素材服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final RoleMapper roleMapper;
    private final RoleConverter roleConverter;
    private final FileResourceService fileResourceService;
    private final MinioUtil minioUtil;

    private static final int DEFAULT_URL_EXPIRY = 3600; // 1小时

    @Override
    @Transactional
    public RoleDTO createRole(CreateRoleRequestDTO createRequest, Long userId) {
        // 验证图片文件是否存在
        validateImageFile(createRequest.getImageUuid());

        // 转换为实体
        Role role = roleConverter.toEntity(createRequest);
        role.setUserId(userId);

        // 保存到数据库
        roleMapper.insert(role);
        log.info("创建角色素材成功，ID: {}, 用户ID: {}", role.getId(), userId);

        // 转换为DTO并设置图片URL
        RoleDTO roleDTO = roleConverter.toDTO(role);
        setRoleImageUrl(roleDTO);
        setRoleTagList(roleDTO);
        
        return roleDTO;
    }

    @Override
    @Transactional
    public RoleDTO updateRole(Long id, UpdateRoleRequestDTO updateRequest, Long userId) {
        // 检查角色是否存在且属于当前用户
        Role existingRole = getRoleByIdAndUserId(id, userId);

        // 如果更新了图片UUID，验证新图片文件是否存在
        if (StringUtils.hasText(updateRequest.getImageUuid())) {
            validateImageFile(updateRequest.getImageUuid());
        }

        // 更新实体
        roleConverter.updateEntity(updateRequest, existingRole);

        // 保存到数据库
        roleMapper.update(existingRole);
        log.info("更新角色素材成功，ID: {}, 用户ID: {}", id, userId);

        // 转换为DTO并设置图片URL
        RoleDTO roleDTO = roleConverter.toDTO(existingRole);
        setRoleImageUrl(roleDTO);
        setRoleTagList(roleDTO);
        
        return roleDTO;
    }

    @Override
    @Transactional
    public boolean deleteRole(Long id, Long userId) {
        // 检查角色是否存在且属于当前用户
        getRoleByIdAndUserId(id, userId);

        // 删除角色
        int result = roleMapper.deleteById(id);
        log.info("删除角色素材，ID: {}, 用户ID: {}, 结果: {}", id, userId, result > 0);
        
        return result > 0;
    }

    @Override
    public RoleDTO getRoleById(Long id, Long userId) {
        Role role = getRoleByIdAndUserId(id, userId);
        
        RoleDTO roleDTO = roleConverter.toDTO(role);
        setRoleImageUrl(roleDTO);
        setRoleTagList(roleDTO);
        
        return roleDTO;
    }

    @Override
    public List<RoleDTO> getUserRoles(Long userId, String name, String tags, Integer page, Integer size) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        int offset = (page - 1) * size;
        List<Role> roles = roleMapper.findByUserId(userId, name, tags, offset, size);
        List<RoleDTO> roleDTOs = roleConverter.toDTOList(roles);
        
        // 为每个角色设置图片URL和标签列表
        roleDTOs.forEach(roleDTO -> {
            setRoleImageUrl(roleDTO);
            setRoleTagList(roleDTO);
        });
        
        return roleDTOs;
    }

    @Override
    public Long countUserRoles(Long userId, String name, String tags) {
        return roleMapper.countByUserId(userId, name, tags);
    }

    @Override
    public List<RoleDTO> getAllRoles(String name, String tags, Integer page, Integer size) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        int offset = (page - 1) * size;
        List<Role> roles = roleMapper.findAll(name, tags, offset, size);
        List<RoleDTO> roleDTOs = roleConverter.toDTOList(roles);
        
        // 为每个角色设置图片URL和标签列表
        roleDTOs.forEach(roleDTO -> {
            setRoleImageUrl(roleDTO);
            setRoleTagList(roleDTO);
        });
        
        return roleDTOs;
    }

    @Override
    public Long countAllRoles(String name, String tags) {
        return roleMapper.countAll(name, tags);
    }

    /**
     * 获取角色并验证所有权
     */
    private Role getRoleByIdAndUserId(Long id, Long userId) {
        Role role = roleMapper.findById(id);
        if (role == null) {
            throw new BusinessException("角色素材不存在");
        }
        
        if (!role.getUserId().equals(userId)) {
            throw new BusinessException("无权访问该角色素材");
        }
        
        return role;
    }

    /**
     * 验证图片文件是否存在
     */
    private void validateImageFile(String imageUuid) {
        try {
            fileResourceService.getByUuid(imageUuid);
        } catch (Exception e) {
            throw new BusinessException("图片文件不存在或无效");
        }
    }

    /**
     * 设置角色图片URL
     */
    private void setRoleImageUrl(RoleDTO roleDTO) {
        if (roleDTO != null && StringUtils.hasText(roleDTO.getImageUuid())) {
            try {
                String imageUrl = fileResourceService.getPreviewUrl(roleDTO.getImageUuid());
                roleDTO.setImageUrl(imageUrl);
            } catch (Exception e) {
                log.warn("获取角色图片URL失败，UUID: {}, 错误: {}", roleDTO.getImageUuid(), e.getMessage());
            }
        }
    }

    /**
     * 设置角色标签列表
     */
    private void setRoleTagList(RoleDTO roleDTO) {
        if (roleDTO != null && StringUtils.hasText(roleDTO.getTags())) {
            List<String> tagList = Arrays.stream(roleDTO.getTags().split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toList());
            roleDTO.setTagList(tagList);
        }
    }
}
