package org.example.novel.service.impl;

import org.example.novel.dto.LoginRequestDTO;
import org.example.novel.dto.LoginResponseDTO;
import org.example.novel.dto.UserDTO;
import org.example.novel.entity.User;
import org.example.novel.exception.BusinessException;
import org.example.novel.converter.UserConverter;
import org.example.novel.service.AuthService;
import org.example.novel.service.UserService;
import org.example.novel.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

/**
 * 认证服务实现类
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserConverter userConverter;

    @Override
    public LoginResponseDTO login(LoginRequestDTO loginRequest) {
        try {
            // 认证用户
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
            );
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 获取用户信息
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            User user = userService.findByUsername(userDetails.getUsername());

            // 生成JWT令牌，包含用户ID
            String token = jwtUtil.generateToken(userDetails, user.getId());

            // 构建登录响应
            return userConverter.toLoginResponseDTO(user, token);
        } catch (Exception e) {
            throw new BusinessException("用户名或密码错误");
        }
    }

    @Override
    public UserDTO register(UserDTO userDTO) {
        // 转换为用户实体（注册时忽略ID）
        User user = userConverter.toEntityForRegister(userDTO);

        // 注册用户
        User registeredUser = userService.register(user);

        // 转换为用户DTO
        return userConverter.toDTO(registeredUser);
    }
} 