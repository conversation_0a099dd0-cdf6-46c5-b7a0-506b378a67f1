package org.example.novel.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.config.AIProperties;
import org.example.novel.exception.BusinessException;
import org.example.novel.service.AIClientService;
import org.example.novel.util.ProxyTestUtil;

import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI客户端服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AIClientServiceImpl implements AIClientService {

    private final AIProperties aiProperties;
    private final WebClient geminiWebClient;
    private final WebClient ollamaWebClient;
    private final ObjectMapper objectMapper;

    @Override
    public String callGemini(String prompt) {
        return executeWithRetry(() -> callGeminiInternal(prompt), aiProperties.getGemini().getRetry());
    }

    private String callGeminiInternal(String prompt) {
        try {
            log.debug("调用Gemini API，提示词长度: {}", prompt.length());

            // 记录代理配置
            if (aiProperties.getGemini().getProxy().getEnabled()) {
                log.debug("使用代理: {}:{}",
                    aiProperties.getGemini().getProxy().getHost(),
                    aiProperties.getGemini().getProxy().getPort());
            }

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> content = new HashMap<>();
            Map<String, Object> part = new HashMap<>();
            part.put("text", prompt);
            content.put("parts", List.of(part));
            requestBody.put("contents", List.of(content));

            // 使用配置好代理的WebClient

            // 发送请求
            String response = geminiWebClient.post()
                    .uri(aiProperties.getGemini().getBaseUrl() + "/models/{model}:generateContent?key={apiKey}",
                         aiProperties.getGemini().getModel(),
                         aiProperties.getGemini().getApiKey())
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(aiProperties.getGemini().getTimeout())
                    .block();

            // 解析响应
            String result = parseGeminiResponse(response);
            log.debug("Gemini响应: {}", result);
            return result;

        } catch (WebClientResponseException e) {
            log.error("Gemini API调用失败，状态码: {}, 响应: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BusinessException("Gemini API调用失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Gemini API调用异常", e);
            throw new BusinessException("Gemini API调用异常: " + e.getMessage());
        }
    }

    @Override
    public String callOllama(String prompt) {
        return executeWithRetry(() -> callOllamaInternal(prompt), aiProperties.getOllama().getRetry());
    }

    private String callOllamaInternal(String prompt) {
        try {
            log.debug("调用Ollama API，提示词长度: {}", prompt.length());

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", aiProperties.getOllama().getModel());
            requestBody.put("prompt", prompt);
            requestBody.put("stream", false);
            
            Map<String, Object> options = new HashMap<>();
            options.put("temperature", aiProperties.getOllama().getTemperature());
            requestBody.put("options", options);

            // 发送请求
            String response = ollamaWebClient.post()
                    .uri(aiProperties.getOllama().getBaseUrl() + "/api/generate")
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(aiProperties.getOllama().getTimeout())
                    .block();

            // 解析响应
            String result = parseOllamaResponse(response);
            log.debug("Ollama响应: {}", result);
            return result;

        } catch (WebClientResponseException e) {
            log.error("Ollama API调用失败，状态码: {}, 响应: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BusinessException("Ollama API调用失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Ollama API调用异常", e);
            throw new BusinessException("Ollama API调用异常: " + e.getMessage());
        }
    }

    @Override
    public String callDefault(String prompt) {
        return call(aiProperties.getDefaultProvider(), prompt);
    }

    @Override
    public String call(String provider, String prompt) {
        switch (provider.toLowerCase()) {
            case "gemini":
                return callGemini(prompt);
            case "ollama":
                return callOllama(prompt);
            default:
                throw new BusinessException("不支持的AI服务提供商: " + provider);
        }
    }

    /**
     * 解析Gemini响应
     */
    private String parseGeminiResponse(String response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode candidates = jsonNode.get("candidates");
            if (candidates != null && candidates.isArray() && candidates.size() > 0) {
                JsonNode content = candidates.get(0).get("content");
                if (content != null) {
                    JsonNode parts = content.get("parts");
                    if (parts != null && parts.isArray() && parts.size() > 0) {
                        JsonNode text = parts.get(0).get("text");
                        if (text != null) {
                            return text.asText();
                        }
                    }
                }
            }
            throw new BusinessException("Gemini响应格式异常");
        } catch (Exception e) {
            log.error("解析Gemini响应失败", e);
            throw new BusinessException("解析Gemini响应失败: " + e.getMessage());
        }
    }

    /**
     * 解析Ollama响应
     */
    private String parseOllamaResponse(String response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode responseNode = jsonNode.get("response");
            if (responseNode != null) {
                return responseNode.asText();
            }
            throw new BusinessException("Ollama响应格式异常");
        } catch (Exception e) {
            log.error("解析Ollama响应失败", e);
            throw new BusinessException("解析Ollama响应失败: " + e.getMessage());
        }
    }

    /**
     * 执行带重试的操作
     */
    private String executeWithRetry(java.util.function.Supplier<String> operation, AIProperties.Retry retryConfig) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= retryConfig.getMaxAttempts(); attempt++) {
            try {
                return operation.get();
            } catch (Exception e) {
                lastException = e;
                log.warn("第{}次尝试失败: {}", attempt, e.getMessage());

                if (attempt < retryConfig.getMaxAttempts()) {
                    try {
                        long delay = calculateDelay(attempt, retryConfig);
                        log.debug("等待{}ms后重试", delay);
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BusinessException("重试被中断");
                    }
                }
            }
        }

        throw new BusinessException("重试失败，最后一次错误: " + lastException.getMessage(), lastException);
    }

    /**
     * 计算重试延迟时间
     */
    private long calculateDelay(int attempt, AIProperties.Retry retryConfig) {
        long delay = retryConfig.getInitialDelay().toMillis();
        for (int i = 1; i < attempt; i++) {
            delay = (long) (delay * retryConfig.getMultiplier());
        }
        return Math.min(delay, retryConfig.getMaxDelay().toMillis());
    }

    @Override
    public boolean testGeminiConnection() {
        return ProxyTestUtil.testGeminiConnection(geminiWebClient, aiProperties.getGemini().getApiKey());
    }

    @Override
    public boolean testOllamaConnection() {
        return ProxyTestUtil.testProxyConnection(ollamaWebClient, aiProperties.getOllama().getBaseUrl() + "/api/tags");
    }
}
