package org.example.novel.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.converter.NovelConverter;
import org.example.novel.dto.CreateNovelRequestDTO;
import org.example.novel.dto.NovelDTO;
import org.example.novel.dto.UpdateNovelRequestDTO;
import org.example.novel.dto.UploadNovelRequestDTO;
import org.example.novel.entity.Novel;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.NovelMapper;
import org.example.novel.service.FileResourceService;
import org.example.novel.service.NovelChapterService;
import org.example.novel.service.NovelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 小说服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NovelServiceImpl implements NovelService {

    private final NovelMapper novelMapper;
    private final NovelConverter novelConverter;
    private final NovelChapterService novelChapterService;
    private final FileResourceService fileResourceService;

    @Override
    @Transactional
    public NovelDTO createNovel(CreateNovelRequestDTO createRequest, Long userId) {
        // 验证封面图片文件是否存在
        if (StringUtils.hasText(createRequest.getCoverUuid())) {
            validateCoverFile(createRequest.getCoverUuid());
        }

        // 转换为实体
        Novel novel = novelConverter.toEntity(createRequest);
        novel.setUserId(userId);

        // 保存到数据库
        novelMapper.insert(novel);
        log.info("创建小说成功，ID: {}, 标题: {}, 用户ID: {}", novel.getId(), novel.getTitle(), userId);

        // 转换为DTO并设置封面URL
        NovelDTO novelDTO = novelConverter.toDTO(novel);
        setCoverUrl(novelDTO);
        
        return novelDTO;
    }

    @Override
    @Transactional
    public NovelDTO uploadNovel(MultipartFile file, UploadNovelRequestDTO uploadRequest, Long userId) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".txt")) {
            throw new BusinessException("只支持txt格式的文件");
        }

        // 读取文件内容
        String content;
        try {
            content = new String(file.getBytes(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取文件内容失败", e);
            throw new BusinessException("读取文件内容失败");
        }

        if (content.trim().isEmpty()) {
            throw new BusinessException("文件内容不能为空");
        }

        // 验证封面图片文件是否存在
        if (StringUtils.hasText(uploadRequest.getCoverUuid())) {
            validateCoverFile(uploadRequest.getCoverUuid());
        }

        // 创建小说
        Novel novel = novelConverter.toEntity(uploadRequest);
        novel.setUserId(userId);
        novelMapper.insert(novel);

        log.info("上传小说成功，ID: {}, 标题: {}, 用户ID: {}", novel.getId(), novel.getTitle(), userId);

        // 创建章节
        try {
            novelChapterService.createChaptersFromContent(
                novel.getId(), 
                content, 
                uploadRequest.getAutoExtractChapters(), 
                userId
            );
        } catch (Exception e) {
            log.error("创建章节失败，小说ID: {}", novel.getId(), e);
            // 如果章节创建失败，不影响小说创建，但需要记录日志
        }

        // 转换为DTO并设置封面URL
        NovelDTO novelDTO = novelConverter.toDTO(novel);
        setCoverUrl(novelDTO);
        
        return novelDTO;
    }

    @Override
    @Transactional
    public NovelDTO updateNovel(Long id, UpdateNovelRequestDTO updateRequest, Long userId) {
        // 检查小说是否存在且属于当前用户
        Novel existingNovel = getNovelByIdAndUserId(id, userId);

        // 如果更新了封面UUID，验证新封面文件是否存在
        if (StringUtils.hasText(updateRequest.getCoverUuid())) {
            validateCoverFile(updateRequest.getCoverUuid());
        }

        // 更新实体
        novelConverter.updateEntity(updateRequest, existingNovel);

        // 保存到数据库
        novelMapper.update(existingNovel);
        log.info("更新小说成功，ID: {}, 用户ID: {}", id, userId);

        // 转换为DTO并设置封面URL
        NovelDTO novelDTO = novelConverter.toDTO(existingNovel);
        setCoverUrl(novelDTO);
        
        return novelDTO;
    }

    @Override
    @Transactional
    public boolean deleteNovel(Long id, Long userId) {
        // 检查小说是否存在且属于当前用户
        getNovelByIdAndUserId(id, userId);

        // 删除所有章节
        try {
            // 这里可以考虑异步删除章节内容文件
            // 暂时先删除数据库记录
        } catch (Exception e) {
            log.warn("删除小说章节时出现异常，小说ID: {}", id, e);
        }

        // 删除小说
        int result = novelMapper.deleteById(id);
        log.info("删除小说，ID: {}, 用户ID: {}, 结果: {}", id, userId, result > 0);
        
        return result > 0;
    }

    @Override
    public NovelDTO getNovelById(Long id, Long userId) {
        Novel novel = getNovelByIdAndUserId(id, userId);
        
        NovelDTO novelDTO = novelConverter.toDTO(novel);
        setCoverUrl(novelDTO);
        
        return novelDTO;
    }

    @Override
    public List<NovelDTO> getUserNovels(Long userId, String title, String author, Integer page, Integer size) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        int offset = (page - 1) * size;
        List<Novel> novels = novelMapper.findByUserId(userId, title, author, offset, size);
        List<NovelDTO> novelDTOs = novelConverter.toDTOList(novels);
        
        // 为每个小说设置封面URL
        novelDTOs.forEach(this::setCoverUrl);
        
        return novelDTOs;
    }

    @Override
    public Long countUserNovels(Long userId, String title, String author) {
        return novelMapper.countByUserId(userId, title, author);
    }

    @Override
    public List<NovelDTO> getAllNovels(String title, String author, Integer page, Integer size) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        int offset = (page - 1) * size;
        List<Novel> novels = novelMapper.findAll(title, author, offset, size);
        List<NovelDTO> novelDTOs = novelConverter.toDTOList(novels);
        
        // 为每个小说设置封面URL
        novelDTOs.forEach(this::setCoverUrl);
        
        return novelDTOs;
    }

    @Override
    public Long countAllNovels(String title, String author) {
        return novelMapper.countAll(title, author);
    }

    /**
     * 获取小说并验证所有权
     */
    private Novel getNovelByIdAndUserId(Long id, Long userId) {
        Novel novel = novelMapper.findById(id);
        if (novel == null) {
            throw new BusinessException("小说不存在");
        }
        
        if (!novel.getUserId().equals(userId)) {
            throw new BusinessException("无权访问该小说");
        }
        
        return novel;
    }

    /**
     * 验证封面文件是否存在
     */
    private void validateCoverFile(String coverUuid) {
        try {
            fileResourceService.getByUuid(coverUuid);
        } catch (Exception e) {
            throw new BusinessException("封面图片文件不存在或无效");
        }
    }

    /**
     * 设置小说封面URL
     */
    private void setCoverUrl(NovelDTO novelDTO) {
        if (novelDTO != null && StringUtils.hasText(novelDTO.getCoverUuid())) {
            try {
                String coverUrl = fileResourceService.getPreviewUrl(novelDTO.getCoverUuid());
                novelDTO.setCoverUrl(coverUrl);
            } catch (Exception e) {
                log.warn("获取小说封面URL失败，UUID: {}, 错误: {}", novelDTO.getCoverUuid(), e.getMessage());
            }
        }
    }
}
