package org.example.novel.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.converter.NovelChapterConverter;
import org.example.novel.dto.BatchUploadChapterResponseDTO;
import org.example.novel.dto.NovelChapterDTO;
import org.example.novel.dto.UploadChapterRequestDTO;
import org.example.novel.entity.Novel;
import org.example.novel.entity.NovelChapter;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.NovelChapterMapper;
import org.example.novel.mapper.NovelMapper;
import org.example.novel.service.FileResourceService;
import org.example.novel.service.NovelChapterService;
import org.example.novel.util.ChapterExtractorUtil;
import org.example.novel.util.ChapterFileNameParser;
import org.example.novel.util.MinioUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 小说章节服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NovelChapterServiceImpl implements NovelChapterService {

    private final NovelChapterMapper novelChapterMapper;
    private final NovelMapper novelMapper;
    private final NovelChapterConverter novelChapterConverter;
    private final FileResourceService fileResourceService;
    private final MinioUtil minioUtil;

    @Override
    @Transactional
    public NovelChapterDTO uploadChapterFile(Long novelId, MultipartFile file, UploadChapterRequestDTO uploadRequest, Long userId) {
        // 验证小说是否存在且属于当前用户
        validateNovelOwnership(novelId, userId);

        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".txt")) {
            throw new BusinessException("只支持txt格式的文件");
        }

        // 读取文件内容
        String content;
        try {
            content = new String(file.getBytes(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取文件内容失败", e);
            throw new BusinessException("读取文件内容失败");
        }

        if (content.trim().isEmpty()) {
            throw new BusinessException("文件内容不能为空");
        }

        // 解析文件名获取章节信息
        ChapterFileNameParser.ParseResult parseResult = ChapterFileNameParser.parseFileName(originalFilename);

        // 确定章节标题和序号
        String title = uploadRequest.getTitle();
        Integer chapterIndex = uploadRequest.getChapterIndex();

        if (!StringUtils.hasText(title)) {
            title = parseResult.getChapterTitle();
        }

        if (chapterIndex == null) {
            chapterIndex = parseResult.getChapterIndex();
        }

        // 如果仍然没有章节序号，自动分配
        if (chapterIndex == null) {
            Integer maxChapterIndex = novelChapterMapper.getMaxChapterIndex(novelId);
            chapterIndex = (maxChapterIndex != null) ? maxChapterIndex + 1 : 1;
        }

        // 检查章节是否已存在
        NovelChapter existingChapter = novelChapterMapper.findByNovelIdAndChapterIndex(novelId, chapterIndex);
        if (existingChapter != null && !Boolean.TRUE.equals(uploadRequest.getOverwrite())) {
            throw new BusinessException("章节" + chapterIndex + "已存在，如需覆盖请设置overwrite=true");
        }

        // 将章节内容存储到MinIO
        String contentUuid = storeChapterContent(content);

        if (existingChapter != null) {
            // 更新现有章节
            existingChapter.setTitle(title);
            existingChapter.setContentUuid(contentUuid);
            existingChapter.setWordCount(content.length());
            novelChapterMapper.update(existingChapter);
            log.info("覆盖章节成功，ID: {}, 小说ID: {}, 章节序号: {}", existingChapter.getId(), novelId, chapterIndex);
            return novelChapterConverter.toDTO(existingChapter);
        } else {
            // 创建新章节
            NovelChapter chapter = NovelChapter.builder()
                    .novelId(novelId)
                    .title(title)
                    .chapterIndex(chapterIndex)
                    .contentUuid(contentUuid)
                    .wordCount(content.length())
                    .isSceneParsed(false)
                    .isCharacterParsed(false)
                    .build();

            novelChapterMapper.insert(chapter);
            log.info("创建章节成功，ID: {}, 小说ID: {}, 章节序号: {}", chapter.getId(), novelId, chapterIndex);

            // 更新小说的章节数量
            updateNovelChapterCount(novelId);

            return novelChapterConverter.toDTO(chapter);
        }
    }

    @Override
    @Transactional
    public BatchUploadChapterResponseDTO batchUploadChapterFiles(Long novelId, MultipartFile[] files, Boolean overwrite, Long userId) {
        // 验证小说是否存在且属于当前用户
        validateNovelOwnership(novelId, userId);

        if (files == null || files.length == 0) {
            throw new BusinessException("文件列表不能为空");
        }

        List<NovelChapterDTO> successChapters = new ArrayList<>();
        List<BatchUploadChapterResponseDTO.FailedUpload> failedUploads = new ArrayList<>();

        for (MultipartFile file : files) {
            try {
                if (file.isEmpty()) {
                    failedUploads.add(BatchUploadChapterResponseDTO.FailedUpload.builder()
                            .fileName(file.getOriginalFilename())
                            .reason("文件为空")
                            .errorCode("EMPTY_FILE")
                            .build());
                    continue;
                }

                UploadChapterRequestDTO uploadRequest = UploadChapterRequestDTO.builder()
                        .overwrite(overwrite)
                        .build();

                NovelChapterDTO chapterDTO = uploadChapterFile(novelId, file, uploadRequest, userId);
                successChapters.add(chapterDTO);

            } catch (Exception e) {
                log.error("上传章节文件失败: {}", file.getOriginalFilename(), e);
                failedUploads.add(BatchUploadChapterResponseDTO.FailedUpload.builder()
                        .fileName(file.getOriginalFilename())
                        .reason(e.getMessage())
                        .errorCode("UPLOAD_FAILED")
                        .build());
            }
        }

        return BatchUploadChapterResponseDTO.builder()
                .successChapters(successChapters)
                .failedUploads(failedUploads)
                .totalFiles(files.length)
                .successCount(successChapters.size())
                .failedCount(failedUploads.size())
                .build();
    }

    @Override
    @Transactional
    public List<NovelChapterDTO> createChaptersFromContent(Long novelId, String content, Boolean autoExtract, Long userId) {
        // 验证小说是否存在且属于当前用户
        validateNovelOwnership(novelId, userId);

        List<NovelChapterDTO> createdChapters = new ArrayList<>();

        if (autoExtract != null && autoExtract) {
            // 自动提取章节
            List<ChapterExtractorUtil.ChapterInfo> extractedChapters = ChapterExtractorUtil.extractChapters(content);
            
            if (ChapterExtractorUtil.validateChapters(extractedChapters)) {
                createdChapters = batchCreateChapters(novelId, extractedChapters, userId);
                log.info("自动提取并创建章节成功，小说ID: {}, 章节数: {}", novelId, createdChapters.size());
            } else {
                log.warn("章节提取验证失败，将整个内容作为单一章节，小说ID: {}", novelId);
                // 如果提取失败，作为单一章节处理
                NovelChapterDTO singleChapter = createChapter(novelId, "正文", content, userId);
                createdChapters.add(singleChapter);
            }
        } else {
            // 不自动提取，整个内容作为一个章节
            NovelChapterDTO singleChapter = createChapter(novelId, "正文", content, userId);
            createdChapters.add(singleChapter);
        }

        // 更新小说的章节数量
        updateNovelChapterCount(novelId);

        return createdChapters;
    }

    @Override
    @Transactional
    public NovelChapterDTO createChapter(Long novelId, String title, String content, Long userId) {
        // 验证小说是否存在且属于当前用户
        validateNovelOwnership(novelId, userId);

        // 获取下一个章节序号
        Integer maxChapterIndex = novelChapterMapper.getMaxChapterIndex(novelId);
        int nextChapterIndex = (maxChapterIndex != null) ? maxChapterIndex + 1 : 1;

        // 将章节内容存储到MinIO
        String contentUuid = storeChapterContent(content);

        // 创建章节实体
        NovelChapter chapter = NovelChapter.builder()
                .novelId(novelId)
                .title(title)
                .chapterIndex(nextChapterIndex)
                .contentUuid(contentUuid)
                .wordCount(content.length())
                .isSceneParsed(false)
                .isCharacterParsed(false)
                .build();

        // 保存到数据库
        novelChapterMapper.insert(chapter);
        log.info("创建章节成功，ID: {}, 小说ID: {}, 标题: {}", chapter.getId(), novelId, title);

        // 更新小说的章节数量
        updateNovelChapterCount(novelId);

        return novelChapterConverter.toDTO(chapter);
    }

    @Override
    @Transactional
    public NovelChapterDTO updateChapter(Long chapterId, String title, String content, Long userId) {
        // 获取章节并验证权限
        NovelChapter existingChapter = getChapterWithPermissionCheck(chapterId, userId);

        // 更新章节内容到MinIO
        String newContentUuid = storeChapterContent(content);

        // 更新章节信息
        existingChapter.setTitle(title);
        existingChapter.setContentUuid(newContentUuid);
        existingChapter.setWordCount(content.length());

        // 保存到数据库
        novelChapterMapper.update(existingChapter);
        log.info("更新章节成功，ID: {}, 标题: {}", chapterId, title);

        return novelChapterConverter.toDTO(existingChapter);
    }

    @Override
    @Transactional
    public boolean deleteChapter(Long chapterId, Long userId) {
        // 获取章节并验证权限
        NovelChapter chapter = getChapterWithPermissionCheck(chapterId, userId);

        // 删除章节
        int result = novelChapterMapper.deleteById(chapterId);
        
        if (result > 0) {
            // 更新小说的章节数量
            updateNovelChapterCount(chapter.getNovelId());
            log.info("删除章节成功，ID: {}, 小说ID: {}", chapterId, chapter.getNovelId());
        }

        return result > 0;
    }

    @Override
    public NovelChapterDTO getChapterById(Long chapterId, Long userId) {
        // 获取章节并验证权限
        NovelChapter chapter = getChapterWithPermissionCheck(chapterId, userId);

        // 获取章节内容
        String content = getChapterContentFromMinio(chapter.getContentUuid());

        return novelChapterConverter.toDTOWithContent(chapter, content);
    }

    @Override
    public List<NovelChapterDTO> getNovelChapters(Long novelId, Long userId, Integer page, Integer size) {
        // 验证小说是否存在且属于当前用户
        validateNovelOwnership(novelId, userId);

        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        int offset = (page - 1) * size;
        List<NovelChapter> chapters = novelChapterMapper.findByNovelId(novelId, offset, size);
        
        return novelChapterConverter.toDTOList(chapters);
    }

    @Override
    public Long countNovelChapters(Long novelId, Long userId) {
        // 验证小说是否存在且属于当前用户
        validateNovelOwnership(novelId, userId);

        return novelChapterMapper.countByNovelId(novelId);
    }

    @Override
    public String getChapterContent(Long chapterId, Long userId) {
        // 获取章节并验证权限
        NovelChapter chapter = getChapterWithPermissionCheck(chapterId, userId);

        // 从MinIO获取章节内容
        return getChapterContentFromMinio(chapter.getContentUuid());
    }

    @Override
    @Transactional
    public List<NovelChapterDTO> batchCreateChapters(Long novelId, List<ChapterExtractorUtil.ChapterInfo> chapters, Long userId) {
        // 验证小说是否存在且属于当前用户
        validateNovelOwnership(novelId, userId);

        List<NovelChapter> chapterEntities = new ArrayList<>();
        List<NovelChapterDTO> createdChapters = new ArrayList<>();

        for (ChapterExtractorUtil.ChapterInfo chapterInfo : chapters) {
            // 将章节内容存储到MinIO
            String contentUuid = storeChapterContent(chapterInfo.getContent());

            // 创建章节实体
            NovelChapter chapter = NovelChapter.builder()
                    .novelId(novelId)
                    .title(chapterInfo.getTitle())
                    .chapterIndex(chapterInfo.getChapterIndex())
                    .contentUuid(contentUuid)
                    .wordCount(chapterInfo.getWordCount())
                    .isSceneParsed(false)
                    .isCharacterParsed(false)
                    .build();

            chapterEntities.add(chapter);
        }

        // 批量插入章节
        if (!chapterEntities.isEmpty()) {
            novelChapterMapper.batchInsert(chapterEntities);
            createdChapters = novelChapterConverter.toDTOList(chapterEntities);
            log.info("批量创建章节成功，小说ID: {}, 章节数: {}", novelId, chapterEntities.size());
        }

        return createdChapters;
    }

    @Override
    @Transactional
    public boolean updateSceneParsedStatus(Long chapterId, Boolean isSceneParsed, Long userId) {
        // 获取章节并验证权限
        getChapterWithPermissionCheck(chapterId, userId);

        int result = novelChapterMapper.updateSceneParsedStatus(chapterId, isSceneParsed);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean updateCharacterParsedStatus(Long chapterId, Boolean isCharacterParsed, Long userId) {
        // 获取章节并验证权限
        getChapterWithPermissionCheck(chapterId, userId);

        int result = novelChapterMapper.updateCharacterParsedStatus(chapterId, isCharacterParsed);
        return result > 0;
    }

    /**
     * 验证小说所有权
     */
    private void validateNovelOwnership(Long novelId, Long userId) {
        Novel novel = novelMapper.findById(novelId);
        if (novel == null) {
            throw new BusinessException("小说不存在");
        }
        if (!novel.getUserId().equals(userId)) {
            throw new BusinessException("无权访问该小说");
        }
    }

    /**
     * 获取章节并验证权限
     */
    private NovelChapter getChapterWithPermissionCheck(Long chapterId, Long userId) {
        NovelChapter chapter = novelChapterMapper.findById(chapterId);
        if (chapter == null) {
            throw new BusinessException("章节不存在");
        }

        // 验证小说所有权
        validateNovelOwnership(chapter.getNovelId(), userId);

        return chapter;
    }

    /**
     * 将章节内容存储到MinIO
     */
    private String storeChapterContent(String content) {
        try {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            String fileName = uuid + ".txt";
            
            ByteArrayInputStream inputStream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8));
            String storedUuid = minioUtil.uploadFile(inputStream, fileName, "text/plain", "texts");
            
            log.debug("章节内容存储到MinIO成功，UUID: {}", storedUuid);
            return storedUuid;
        } catch (Exception e) {
            log.error("存储章节内容到MinIO失败", e);
            throw new BusinessException("存储章节内容失败");
        }
    }

    /**
     * 从MinIO获取章节内容
     */
    private String getChapterContentFromMinio(String contentUuid) {
        try {
            // 从MinIO下载文件内容
            InputStream inputStream = minioUtil.downloadFile(contentUuid,"texts");
            byte[] fileBytes = inputStream.readAllBytes();
            inputStream.close();

            String content = new String(fileBytes, StandardCharsets.UTF_8);
            log.debug("从MinIO获取章节内容成功，UUID: {}, 内容长度: {}", contentUuid, content.length());
            return content;
        } catch (Exception e) {
            log.error("从MinIO获取章节内容失败，UUID: {}", contentUuid, e);
            throw new BusinessException("获取章节内容失败: " + e.getMessage());
        }
    }

    /**
     * 更新小说的章节数量
     */
    private void updateNovelChapterCount(Long novelId) {
        Long chapterCount = novelChapterMapper.countByNovelId(novelId);
        novelMapper.updateChapterCount(novelId, chapterCount.intValue());
    }
}
