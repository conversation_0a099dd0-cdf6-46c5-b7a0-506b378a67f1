package org.example.novel.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.converter.FileResourceConverter;
import org.example.novel.dto.FileResourceDTO;
import org.example.novel.dto.MD5CheckRequestDTO;
import org.example.novel.dto.MD5CheckResponseDTO;
import org.example.novel.entity.FileResource;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.FileResourceMapper;
import org.example.novel.service.FileResourceService;
import org.example.novel.util.MD5Util;
import org.example.novel.util.MinioUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.UUID;

/**
 * 文件资源服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileResourceServiceImpl implements FileResourceService {

    private final FileResourceMapper fileResourceMapper;
    private final FileResourceConverter fileResourceConverter;
    private final MinioUtil minioUtil;

    private static final int DEFAULT_URL_EXPIRY = 3600; // 1小时
    private static final int PREVIEW_URL_EXPIRY = 1800; // 30分钟

    @Override
    public MD5CheckResponseDTO checkMD5(MD5CheckRequestDTO request) {
        FileResource existingFile = fileResourceMapper.findByMd5(request.getMd5());
        
        if (existingFile != null) {
            FileResourceDTO fileResourceDTO = fileResourceConverter.toDTO(existingFile);
            // 设置访问URL
            setFileUrls(fileResourceDTO);
            
            return MD5CheckResponseDTO.builder()
                    .exists(true)
                    .fileResource(fileResourceDTO)
                    .message("文件已存在，可直接使用")
                    .build();
        } else {
            return MD5CheckResponseDTO.builder()
                    .exists(false)
                    .message("文件不存在，需要上传")
                    .build();
        }
    }

    @Override
    @Transactional
    public FileResourceDTO uploadFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        // 计算文件MD5
        String md5 = MD5Util.calculateMD5(file);
        if (md5 == null) {
            throw new BusinessException("计算文件MD5失败");
        }

        // 检查文件是否已存在
        FileResource existingFile = fileResourceMapper.findByMd5(md5);
        if (existingFile != null) {
            log.info("文件已存在，MD5: {}", md5);
            FileResourceDTO dto = fileResourceConverter.toDTO(existingFile);
            setFileUrls(dto);
            return dto;
        }

        // 根据MIME类型选择存储桶
        String mimeType = file.getContentType();
        String bucket = minioUtil.getBucketByMimeType(mimeType);

        // 上传文件到MinIO
        String uuid = minioUtil.uploadFile(file, file.getOriginalFilename(), bucket);

        // 保存文件信息到数据库
        FileResource fileResource = FileResource.builder()
                .uuid(uuid)
                .originalName(file.getOriginalFilename())
                .md5(md5)
                .size(file.getSize())
                .mimeType(mimeType)
                .bucket(bucket)
                .build();

        fileResourceMapper.insert(fileResource);
        log.info("文件上传成功，UUID: {}, MD5: {}", uuid, md5);

        FileResourceDTO dto = fileResourceConverter.toDTO(fileResource);
        setFileUrls(dto);
        return dto;
    }

    @Override
    public FileResourceDTO getByUuid(String uuid) {
        FileResource fileResource = fileResourceMapper.findByUuid(uuid);
        if (fileResource == null) {
            throw new BusinessException("文件不存在");
        }
        
        FileResourceDTO dto = fileResourceConverter.toDTO(fileResource);
        setFileUrls(dto);
        return dto;
    }

    @Override
    public FileResourceDTO getById(Long id) {
        FileResource fileResource = fileResourceMapper.findById(id);
        if (fileResource == null) {
            throw new BusinessException("文件不存在");
        }
        
        FileResourceDTO dto = fileResourceConverter.toDTO(fileResource);
        setFileUrls(dto);
        return dto;
    }

    @Override
    public List<FileResourceDTO> getFileList(String bucket, String mimeType, Integer page, Integer size) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        int offset = (page - 1) * size;
        List<FileResource> fileResources = fileResourceMapper.findList(bucket, mimeType, offset, size);
        List<FileResourceDTO> dtoList = fileResourceConverter.toDTOList(fileResources);
        
        // 为每个文件设置访问URL
        dtoList.forEach(this::setFileUrls);
        
        return dtoList;
    }

    @Override
    public Long countFiles(String bucket, String mimeType) {
        return fileResourceMapper.countList(bucket, mimeType);
    }

    @Override
    public String getDownloadUrl(String uuid) {
        FileResource fileResource = fileResourceMapper.findByUuid(uuid);
        if (fileResource == null) {
            throw new BusinessException("文件不存在");
        }
        
        return minioUtil.getFileUrl(fileResource.getBucket(), fileResource.getUuid(), DEFAULT_URL_EXPIRY);
    }

    @Override
    public String getPreviewUrl(String uuid) {
        FileResource fileResource = fileResourceMapper.findByUuid(uuid);
        if (fileResource == null) {
            throw new BusinessException("文件不存在");
        }
        
        // 只有图片和音频支持预览
        String mimeType = fileResource.getMimeType();
        if (mimeType == null || (!mimeType.startsWith("image/") && !mimeType.startsWith("audio/"))) {
            throw new BusinessException("该文件类型不支持预览");
        }
        
        return minioUtil.getFileUrl(fileResource.getBucket(), fileResource.getUuid(), PREVIEW_URL_EXPIRY);
    }

    @Override
    @Transactional
    public boolean deleteFile(String uuid) {
        FileResource fileResource = fileResourceMapper.findByUuid(uuid);
        if (fileResource == null) {
            throw new BusinessException("文件不存在");
        }
        
        try {
            // 删除MinIO中的文件
            minioUtil.deleteFile(fileResource.getBucket(), fileResource.getUuid());
            
            // 删除数据库记录
            int result = fileResourceMapper.deleteByUuid(uuid);
            
            log.info("文件删除成功，UUID: {}", uuid);
            return result > 0;
        } catch (Exception e) {
            log.error("删除文件失败，UUID: {}, 错误: {}", uuid, e.getMessage(), e);
            throw new BusinessException("删除文件失败");
        }
    }

    @Override
    public FileResource getByMd5(String md5) {
        return fileResourceMapper.findByMd5(md5);
    }

    /**
     * 为文件DTO设置访问URL
     *
     * @param dto 文件DTO
     */
    private void setFileUrls(FileResourceDTO dto) {
        if (dto == null || dto.getUuid() == null) {
            return;
        }
        
        try {
            FileResource fileResource = fileResourceMapper.findByUuid(dto.getUuid());
            if (fileResource != null) {
                // 设置下载URL
                dto.setDownloadUrl(minioUtil.getFileUrl(fileResource.getBucket(), fileResource.getUuid(), DEFAULT_URL_EXPIRY));
                dto.setAccessUrl(dto.getDownloadUrl());
                
                // 如果是图片或音频，设置预览URL
                String mimeType = fileResource.getMimeType();
                if (mimeType != null && (mimeType.startsWith("image/") || mimeType.startsWith("audio/"))) {
                    dto.setPreviewUrl(minioUtil.getFileUrl(fileResource.getBucket(), fileResource.getUuid(), PREVIEW_URL_EXPIRY));
                }
            }
        } catch (Exception e) {
            log.warn("设置文件URL失败，UUID: {}, 错误: {}", dto.getUuid(), e.getMessage());
        }
    }
}
