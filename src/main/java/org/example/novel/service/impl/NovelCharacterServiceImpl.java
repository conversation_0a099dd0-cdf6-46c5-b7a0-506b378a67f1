package org.example.novel.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.converter.NovelCharacterConverter;
import org.example.novel.dto.*;
import org.example.novel.entity.NovelCharacter;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.NovelCharacterMapper;
import org.example.novel.mapper.NovelMapper;
import org.example.novel.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 小说角色管理服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NovelCharacterServiceImpl implements NovelCharacterService {

    private final NovelCharacterMapper novelCharacterMapper;
    private final NovelMapper novelMapper;
    private final NovelCharacterConverter novelCharacterConverter;
    private final CharacterParseService characterParseService;
    private final RoleService roleService;
    private final AudioService audioService;
    private final AsyncTaskService asyncTaskService;

    @Override
    public String parseAndCreateCharactersAsync(CharacterParseRequestDTO parseRequest, Long userId) {
        // 快速验证：只检查小说是否存在，详细验证放到异步线程中
        if (parseRequest.getNovelId() == null) {
            throw new BusinessException("小说ID不能为空");
        }

        // 立即调用异步解析服务（验证逻辑在异步线程中执行）
        return characterParseService.parseCharactersAsync(parseRequest, userId);
    }

    @Override
    @Transactional
    @Deprecated
    public CharacterParseResponseDTO parseAndCreateCharacters(CharacterParseRequestDTO parseRequest, Long userId) {
        // 验证小说所有权
        validateNovelOwnership(parseRequest.getNovelId(), userId);

        // 使用AI解析角色
        CharacterParseResponseDTO parseResponse = characterParseService.parseCharacters(parseRequest, userId);

        // 创建角色记录
        List<NovelCharacterDTO> createdCharacters = new ArrayList<>();
        
        // 这里需要从解析服务获取角色名称列表，然后创建角色记录
        // 由于当前解析响应中没有角色名称，我们需要重新设计或者在这里重新解析
        
        return CharacterParseResponseDTO.builder()
                .parsedCharacters(createdCharacters)
                .failedChapters(parseResponse.getFailedChapters())
                .totalChapters(parseResponse.getTotalChapters())
                .successCount(parseResponse.getSuccessCount())
                .failedCount(parseResponse.getFailedCount())
                .totalCharacters(createdCharacters.size())
                .build();
    }

    @Override
    @Transactional
    public NovelCharacterDTO createCharacter(Long novelId, String name, String description, Long userId) {
        // 验证小说所有权
        validateNovelOwnership(novelId, userId);

        // 检查角色名称是否已存在
        if (novelCharacterMapper.existsByNovelIdAndName(novelId, name, null)) {
            throw new BusinessException("角色名称已存在: " + name);
        }

        // 创建角色实体
        NovelCharacter character = NovelCharacter.builder()
                .novelId(novelId)
                .name(name)
                .description(description)
                .build();

        // 保存到数据库
        novelCharacterMapper.insert(character);
        log.info("创建角色成功，ID: {}, 名称: {}, 小说ID: {}", character.getId(), name, novelId);

        // 如果启用自动关联，尝试关联角色素材
        NovelCharacterDTO characterDTO = novelCharacterConverter.toDTO(character);
        autoLinkRoleMaterial(character.getId(), userId);
        
        return getCharacterById(character.getId(), userId);
    }

    @Override
    @Transactional
    public NovelCharacterDTO updateCharacter(Long characterId, String name, String description, Long userId) {
        // 获取角色并验证权限
        NovelCharacter existingCharacter = getCharacterWithPermissionCheck(characterId, userId);

        // 检查角色名称是否已存在（排除当前角色）
        if (StringUtils.hasText(name) && 
            novelCharacterMapper.existsByNovelIdAndName(existingCharacter.getNovelId(), name, characterId)) {
            throw new BusinessException("角色名称已存在: " + name);
        }

        // 更新角色信息
        if (StringUtils.hasText(name)) {
            existingCharacter.setName(name);
        }
        if (StringUtils.hasText(description)) {
            existingCharacter.setDescription(description);
        }

        // 保存到数据库
        novelCharacterMapper.update(existingCharacter);
        log.info("更新角色成功，ID: {}, 名称: {}", characterId, existingCharacter.getName());

        return getCharacterById(characterId, userId);
    }

    @Override
    @Transactional
    public NovelCharacterDTO linkRoleMaterial(Long characterId, Long roleId, Long userId) {
        // 获取角色并验证权限
        NovelCharacter character = getCharacterWithPermissionCheck(characterId, userId);

        // 验证角色素材是否存在且属于当前用户
        try {
            roleService.getRoleById(roleId, userId);
        } catch (Exception e) {
            throw new BusinessException("角色素材不存在或无权访问");
        }

        // 更新关联
        character.setRoleId(roleId);
        novelCharacterMapper.update(character);
        
        log.info("关联角色素材成功，角色ID: {}, 素材ID: {}", characterId, roleId);
        return getCharacterById(characterId, userId);
    }

    @Override
    @Transactional
    public NovelCharacterDTO linkAudioMaterial(Long characterId, Long audioId, Long userId) {
        // 获取角色并验证权限
        NovelCharacter character = getCharacterWithPermissionCheck(characterId, userId);

        // 验证音频素材是否存在且属于当前用户
        try {
            audioService.getAudioById(audioId, userId);
        } catch (Exception e) {
            throw new BusinessException("音频素材不存在或无权访问");
        }

        // 更新关联
        character.setAudioId(audioId);
        novelCharacterMapper.update(character);
        
        log.info("关联音频素材成功，角色ID: {}, 素材ID: {}", characterId, audioId);
        return getCharacterById(characterId, userId);
    }

    @Override
    @Transactional
    public boolean deleteCharacter(Long characterId, Long userId) {
        // 获取角色并验证权限
        getCharacterWithPermissionCheck(characterId, userId);

        // 删除角色
        int result = novelCharacterMapper.deleteById(characterId);
        log.info("删除角色，ID: {}, 结果: {}", characterId, result > 0);
        
        return result > 0;
    }

    @Override
    public NovelCharacterDTO getCharacterById(Long characterId, Long userId) {
        NovelCharacter character = getCharacterWithPermissionCheck(characterId, userId);
        
        NovelCharacterDTO characterDTO = novelCharacterConverter.toDTO(character);
        
        // 设置关联的素材信息
        setMaterialInfo(characterDTO, userId);
        
        return characterDTO;
    }

    @Override
    public List<NovelCharacterDTO> getNovelCharacters(Long novelId, Long userId, Integer page, Integer size) {
        // 验证小说所有权
        validateNovelOwnership(novelId, userId);

        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        int offset = (page - 1) * size;
        List<NovelCharacter> characters = novelCharacterMapper.findByNovelIdWithPaging(novelId, offset, size);
        List<NovelCharacterDTO> characterDTOs = novelCharacterConverter.toDTOList(characters);
        
        // 为每个角色设置素材信息
        characterDTOs.forEach(dto -> setMaterialInfo(dto, userId));
        
        return characterDTOs;
    }

    @Override
    public Long countNovelCharacters(Long novelId, Long userId) {
        // 验证小说所有权
        validateNovelOwnership(novelId, userId);

        return novelCharacterMapper.countByNovelId(novelId);
    }

    @Override
    public boolean autoLinkRoleMaterial(Long characterId, Long userId) {
        NovelCharacter character = getCharacterWithPermissionCheck(characterId, userId);
        
        if (character.getRoleId() != null) {
            return true; // 已经关联了素材
        }

        try {
            // 获取用户的角色素材列表，尝试按名称匹配
            List<RoleDTO> userRoles = roleService.getUserRoles(userId, character.getName(), null, 1, 10);
            
            for (RoleDTO role : userRoles) {
                // 简单的名称匹配逻辑
                if (role.getName().contains(character.getName()) || character.getName().contains(role.getName())) {
                    character.setRoleId(role.getId());
                    novelCharacterMapper.update(character);
                    log.info("自动关联角色素材成功，角色: {} -> 素材: {}", character.getName(), role.getName());
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("自动关联角色素材失败，角色ID: {}", characterId, e);
        }

        return false;
    }

    /**
     * 获取角色并验证权限
     */
    private NovelCharacter getCharacterWithPermissionCheck(Long characterId, Long userId) {
        NovelCharacter character = novelCharacterMapper.findById(characterId);
        if (character == null) {
            throw new BusinessException("角色不存在");
        }

        // 验证小说所有权
        validateNovelOwnership(character.getNovelId(), userId);

        return character;
    }

    /**
     * 验证小说所有权
     */
    private void validateNovelOwnership(Long novelId, Long userId) {
        if (!novelMapper.existsByIdAndUserId(novelId, userId)) {
            throw new BusinessException("无权访问该小说");
        }
    }

    /**
     * 设置角色的素材信息
     */
    private void setMaterialInfo(NovelCharacterDTO characterDTO, Long userId) {
        try {
            // 设置角色素材信息
            if (characterDTO.getRoleId() != null) {
                RoleDTO roleInfo = roleService.getRoleById(characterDTO.getRoleId(), userId);
                characterDTO.setRoleInfo(roleInfo);
            }

            // 设置音频素材信息
            if (characterDTO.getAudioId() != null) {
                AudioDTO audioInfo = audioService.getAudioById(characterDTO.getAudioId(), userId);
                characterDTO.setAudioInfo(audioInfo);
            }
        } catch (Exception e) {
            log.warn("获取角色素材信息失败，角色ID: {}", characterDTO.getId(), e);
        }
    }
}
