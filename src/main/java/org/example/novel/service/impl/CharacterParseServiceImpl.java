package org.example.novel.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.dto.CharacterParseRequestDTO;
import org.example.novel.dto.CharacterParseResponseDTO;
import org.example.novel.entity.NovelChapter;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.NovelChapterMapper;
import org.example.novel.mapper.NovelMapper;
import org.example.novel.service.CharacterParseService;
import org.example.novel.service.NovelChapterService;
import org.example.novel.config.PromptProperties;
import org.example.novel.converter.NovelCharacterConverter;
import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.dto.NovelCharacterDTO;
import org.example.novel.entity.NovelCharacter;
import org.example.novel.mapper.NovelCharacterMapper;
import org.example.novel.service.AIClientService;
import org.example.novel.service.AsyncTaskService;
import org.springframework.beans.factory.annotation.Qualifier;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色解析服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CharacterParseServiceImpl implements CharacterParseService {

    private final AIClientService aiClientService;
    private final PromptProperties promptProperties;
    private final AsyncTaskService asyncTaskService;
    private final NovelCharacterMapper novelCharacterMapper;
    private final NovelCharacterConverter novelCharacterConverter;
    private final Executor taskExecutor;
    private final NovelChapterMapper novelChapterMapper;
    private final NovelMapper novelMapper;
    private final NovelChapterService novelChapterService;



    @Override
    public String parseCharactersAsync(CharacterParseRequestDTO parseRequest, Long userId) {
        // 生成任务ID（立即返回，不做任何耗时操作）
        String taskId = "character_parse_" + System.currentTimeMillis() + "_" + userId;

        log.info("创建异步角色解析任务: {}, 主线程: {}", taskId, Thread.currentThread().getName());

        // 使用CompletableFuture实现真正的异步执行
        CompletableFuture.runAsync(() -> {
            executeCharacterParseTask(taskId, parseRequest, userId);
        }, taskExecutor);

        // 立即返回taskId
        return taskId;
    }



    @Override
    @Deprecated
    public CharacterParseResponseDTO parseCharacters(CharacterParseRequestDTO parseRequest, Long userId) {
        // 验证小说所有权（在异步线程中执行，不影响taskId返回速度）
        validateNovelOwnership(parseRequest.getNovelId(), userId);

        // 批量验证章节所有权（一次查询，提高效率）
        validateChaptersOwnership(parseRequest.getNovelId(), parseRequest.getChapterIds(), userId);

        List<CharacterParseResponseDTO.FailedChapter> failedChapters = new ArrayList<>();
        List<String> allCharacters = new ArrayList<>();
        int successCount = 0;

        int totalChapters = parseRequest.getChapterIds().size();
        int processedChapters = 0;

        for (Long chapterId : parseRequest.getChapterIds()) {
            try {
                // 更新进度
                int progress = 20 + (processedChapters * 70 / totalChapters);

                NovelChapter chapter = novelChapterMapper.findById(chapterId);
                String chapterTitle = chapter != null ? chapter.getTitle() : "章节" + chapterId;

                log.debug("解析章节: {} ({})", chapterTitle, chapterId);

                List<String> chapterCharacters = parseSingleChapter(parseRequest.getNovelId(), chapterId, userId);
                allCharacters.addAll(chapterCharacters);
                successCount++;

                // 更新章节的角色解析状态
                novelChapterMapper.updateCharacterParsedStatus(chapterId, true);

                processedChapters++;
                log.debug("章节解析完成: {}, 找到角色: {}", chapterTitle, chapterCharacters.size());

            } catch (Exception e) {
                log.error("解析章节角色失败，章节ID: {}", chapterId, e);

                NovelChapter chapter = novelChapterMapper.findById(chapterId);
                failedChapters.add(CharacterParseResponseDTO.FailedChapter.builder()
                        .chapterId(chapterId)
                        .chapterTitle(chapter != null ? chapter.getTitle() : "未知章节")
                        .reason(e.getMessage())
                        .errorCode("PARSE_FAILED")
                        .build());

                processedChapters++;
            }
        }

        // 去重角色名称
        List<String> uniqueCharacters = allCharacters.stream()
                .distinct()
                .collect(Collectors.toList());

        // 创建角色记录
        List<NovelCharacterDTO> createdCharacters = new ArrayList<>();
        for (String characterName : uniqueCharacters) {
            try {
                // 检查角色是否已存在
                if (!novelCharacterMapper.existsByNovelIdAndName(parseRequest.getNovelId(), characterName, null)) {
                    // 创建新角色
                    NovelCharacter character = NovelCharacter.builder()
                            .novelId(parseRequest.getNovelId())
                            .name(characterName)
                            .description("AI解析生成的角色")
                            .build();

                    novelCharacterMapper.insert(character);

                    // 转换为DTO
                    NovelCharacterDTO characterDTO = novelCharacterConverter.toDTO(character);
                    createdCharacters.add(characterDTO);

                    log.debug("创建角色成功: {}", characterName);
                } else {
                    log.debug("角色已存在，跳过创建: {}", characterName);
                }
            } catch (Exception e) {
                log.error("创建角色失败: {}", characterName, e);
            }
        }

        return CharacterParseResponseDTO.builder()
                .parsedCharacters(createdCharacters)
                .failedChapters(failedChapters)
                .totalChapters(parseRequest.getChapterIds().size())
                .successCount(successCount)
                .failedCount(failedChapters.size())
                .totalCharacters(createdCharacters.size())
                .build();
    }

    @Override
    public List<String> parseSingleChapter(Long novelId, Long chapterId, Long userId) {
        // 验证章节所有权
        NovelChapter chapter = novelChapterMapper.findById(chapterId);
        if (chapter == null) {
            throw new BusinessException("章节不存在");
        }
        
        if (!chapter.getNovelId().equals(novelId)) {
            throw new BusinessException("章节不属于指定小说");
        }

        // 验证小说所有权
        validateNovelOwnership(novelId, userId);

        // 获取章节内容
        String content = novelChapterService.getChapterContent(chapterId, userId);
        if (!StringUtils.hasText(content)) {
            throw new BusinessException("章节内容为空");
        }

        // 使用AI解析角色
        return parseCharactersFromText(content);
    }

    @Override
    public List<String> parseCharactersFromText(String content) {
        try {
            log.debug("开始使用AI解析角色，内容长度: {}", content.length());

            // 构建完整的提示词
            String promptTemplate = promptProperties.getCharacterParse();
            String fullPrompt = promptTemplate.replace("{content}", content);

            // 调用AI模型（使用Gemini进行角色解析）
            String response = aiClientService.callGemini(fullPrompt);

            log.debug("AI响应: {}", response);

            // 解析响应
            List<String> characters = parseAIResponse(response);
            
            log.info("成功解析出 {} 个角色: {}", characters.size(), characters);
            return characters;

        } catch (Exception e) {
            log.error("AI角色解析失败", e);
            throw new BusinessException("AI角色解析失败: " + e.getMessage());
        }
    }

    /**
     * 解析AI响应，提取角色名称
     */
    private List<String> parseAIResponse(String response) {
        if (!StringUtils.hasText(response)) {
            return new ArrayList<>();
        }

        // 按行分割响应
        String[] lines = response.split("\n");
        List<String> characters = new ArrayList<>();

        for (String line : lines) {
            String trimmedLine = line.trim();
            
            // 跳过空行和特殊标记
            if (trimmedLine.isEmpty() || 
                trimmedLine.equals("无") || 
                trimmedLine.startsWith("#") ||
                trimmedLine.startsWith("角色") ||
                trimmedLine.length() < 2) {
                continue;
            }

            // 移除可能的序号
            trimmedLine = trimmedLine.replaceFirst("^\\d+\\.?\\s*", "");
            trimmedLine = trimmedLine.replaceFirst("^[一二三四五六七八九十]+\\.?\\s*", "");
            
            if (StringUtils.hasText(trimmedLine) && trimmedLine.length() >= 2) {
                characters.add(trimmedLine);
            }
        }

        return characters.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 异步执行角色解析任务
     */
    private void executeCharacterParseTask(String taskId, CharacterParseRequestDTO parseRequest, Long userId) {
        LocalDateTime startTime = LocalDateTime.now();

        try {
            log.info("开始异步角色解析任务: {}, 异步线程: {}", taskId, Thread.currentThread().getName());

            // 创建初始任务状态（在异步线程中）
            AsyncTaskResultDTO<CharacterParseResponseDTO> taskResult = AsyncTaskResultDTO.pending(
                taskId,
                "角色解析任务 - 小说ID: " + parseRequest.getNovelId()
            );
            taskResult.setStartTime(startTime);
            asyncTaskService.saveTaskStatus(taskResult, userId);

            // 更新进度：验证权限
            asyncTaskService.updateTaskProgress(taskId, 10, "验证小说权限", userId);

            // 执行同步解析逻辑
            CharacterParseResponseDTO result = parseCharacters(parseRequest, userId);

            // 标记任务完成
            asyncTaskService.completeTask(taskId, result, userId);
            log.info("异步角色解析任务完成: {}, 耗时: {}ms, 异步线程: {}", taskId,
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis(),
                    Thread.currentThread().getName());

        } catch (Exception e) {
            log.error("异步角色解析任务失败: {}, 异步线程: {}", taskId, Thread.currentThread().getName(), e);

            // 如果任务状态还没保存，先保存一个失败状态
            try {
                AsyncTaskResultDTO<CharacterParseResponseDTO> failedTask = AsyncTaskResultDTO.failed(
                    taskId,
                    "角色解析任务 - 小说ID: " + parseRequest.getNovelId(),
                    e.getMessage(),
                    startTime
                );
                asyncTaskService.saveTaskStatus(failedTask, userId);
            } catch (Exception saveException) {
                // 如果任务已存在，直接标记失败
                asyncTaskService.failTask(taskId, e.getMessage(), userId);
            }
        }
    }

    /**
     * 验证小说所有权
     */
    private void validateNovelOwnership(Long novelId, Long userId) {
        if (!novelMapper.existsByIdAndUserId(novelId, userId)) {
            throw new BusinessException("无权访问该小说");
        }
    }

    /**
     * 批量验证章节所有权
     */
    private void validateChaptersOwnership(Long novelId, List<Long> chapterIds, Long userId) {
        for (Long chapterId : chapterIds) {
            NovelChapter chapter = novelChapterMapper.findById(chapterId);
            if (chapter == null) {
                throw new BusinessException("章节不存在: " + chapterId);
            }
            if (!chapter.getNovelId().equals(novelId)) {
                throw new BusinessException("章节不属于指定小说: " + chapterId);
            }
        }
    }
}
