package org.example.novel.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.converter.ChapterSceneConverter;
import org.example.novel.dto.ChapterSceneDTO;
import org.example.novel.dto.SceneParseRequestDTO;
import org.example.novel.dto.SceneParseResponseDTO;
import org.example.novel.entity.ChapterScene;
import org.example.novel.entity.NovelCharacter;
import org.example.novel.entity.NovelChapter;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.ChapterSceneMapper;
import org.example.novel.mapper.NovelCharacterMapper;
import org.example.novel.mapper.NovelChapterMapper;
import org.example.novel.service.ChapterSceneService;
import org.example.novel.service.SceneParseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 章节分镜管理服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChapterSceneServiceImpl implements ChapterSceneService {

    private final SceneParseService sceneParseService;
    private final ChapterSceneMapper chapterSceneMapper;
    private final NovelChapterMapper novelChapterMapper;
    private final NovelCharacterMapper novelCharacterMapper;
    private final ChapterSceneConverter chapterSceneConverter;

    @Override
    public String parseAndCreateScenesAsync(SceneParseRequestDTO parseRequest, Long userId) {
        // 验证小说所有权
        validateNovelOwnership(parseRequest.getNovelId(), userId);
        
        // 调用异步解析服务
        return sceneParseService.parseSceneAsync(parseRequest, userId);
    }

    @Override
    @Transactional
    @Deprecated
    public SceneParseResponseDTO parseAndCreateScenes(SceneParseRequestDTO parseRequest, Long userId) {
        // 验证小说所有权
        validateNovelOwnership(parseRequest.getNovelId(), userId);
        
        // 调用解析服务
        SceneParseResponseDTO response = sceneParseService.parseScene(parseRequest, userId);
        
        // 如果需要自动关联角色
        if (parseRequest.getAutoLinkCharacter()) {
            for (Long chapterId : parseRequest.getChapterIds()) {
                try {
                    autoLinkCharacters(chapterId, userId);
                } catch (Exception e) {
                    log.warn("自动关联角色失败，章节ID: {}", chapterId, e);
                }
            }
        }
        
        return response;
    }

    @Override
    public List<ChapterSceneDTO> getChapterScenes(Long chapterId, Long userId) {
        // 验证章节权限
        validateChapterOwnership(chapterId, userId);
        
        List<ChapterScene> scenes = chapterSceneMapper.findByChapterId(chapterId);
        return scenes.stream()
                .map(chapterSceneConverter::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ChapterSceneDTO getSceneDetail(Long sceneId, Long userId) {
        ChapterScene scene = chapterSceneMapper.findById(sceneId);
        if (scene == null) {
            throw new BusinessException("分镜不存在");
        }
        
        // 验证章节权限
        validateChapterOwnership(scene.getChapterId(), userId);
        
        return chapterSceneConverter.toDTO(scene);
    }

    @Override
    @Transactional
    public ChapterSceneDTO updateScene(Long sceneId, ChapterSceneDTO sceneDTO, Long userId) {
        ChapterScene existingScene = chapterSceneMapper.findById(sceneId);
        if (existingScene == null) {
            throw new BusinessException("分镜不存在");
        }
        
        // 验证章节权限
        validateChapterOwnership(existingScene.getChapterId(), userId);
        
        // 更新分镜信息
        ChapterScene updateScene = ChapterScene.builder()
                .id(sceneId)
                .content(sceneDTO.getContent())
                .prompt(sceneDTO.getPrompt())
                .speaker(sceneDTO.getSpeaker())
                .characterId(sceneDTO.getCharacterId())
                .build();
        
        chapterSceneMapper.update(updateScene);
        
        // 返回更新后的分镜
        ChapterScene updatedScene = chapterSceneMapper.findById(sceneId);
        return chapterSceneConverter.toDTO(updatedScene);
    }

    @Override
    @Transactional
    public void deleteScene(Long sceneId, Long userId) {
        ChapterScene scene = chapterSceneMapper.findById(sceneId);
        if (scene == null) {
            throw new BusinessException("分镜不存在");
        }
        
        // 验证章节权限
        validateChapterOwnership(scene.getChapterId(), userId);
        
        chapterSceneMapper.deleteById(sceneId);
        log.info("删除分镜成功，分镜ID: {}", sceneId);
    }

    @Override
    @Transactional
    public ChapterSceneDTO linkCharacter(Long sceneId, Long characterId, Long userId) {
        ChapterScene scene = chapterSceneMapper.findById(sceneId);
        if (scene == null) {
            throw new BusinessException("分镜不存在");
        }
        
        // 验证章节权限
        validateChapterOwnership(scene.getChapterId(), userId);
        
        // 验证角色权限
        if (characterId != null) {
            NovelCharacter character = novelCharacterMapper.findById(characterId);
            if (character == null) {
                throw new BusinessException("角色不存在");
            }
            
            // 获取章节信息验证小说ID
            NovelChapter chapter = novelChapterMapper.findById(scene.getChapterId());
            if (!character.getNovelId().equals(chapter.getNovelId())) {
                throw new BusinessException("角色不属于该小说");
            }
        }
        
        // 更新角色关联
        chapterSceneMapper.updateCharacterLink(sceneId, characterId);
        
        // 返回更新后的分镜
        ChapterScene updatedScene = chapterSceneMapper.findById(sceneId);
        return chapterSceneConverter.toDTO(updatedScene);
    }

    @Override
    @Transactional
    public Integer autoLinkCharacters(Long chapterId, Long userId) {
        // 验证章节权限
        validateChapterOwnership(chapterId, userId);
        
        // 获取章节信息
        NovelChapter chapter = novelChapterMapper.findById(chapterId);
        if (chapter == null) {
            throw new BusinessException("章节不存在");
        }
        
        // 获取该小说的所有角色
        List<NovelCharacter> characters = novelCharacterMapper.findByNovelId(chapter.getNovelId());
        
        // 获取章节的所有分镜
        List<ChapterScene> scenes = chapterSceneMapper.findByChapterId(chapterId);
        
        int linkedCount = 0;
        
        for (ChapterScene scene : scenes) {
            if (StringUtils.hasText(scene.getSpeaker())) {
                // 查找匹配的角色
                NovelCharacter matchedCharacter = characters.stream()
                        .filter(character -> character.getName().equals(scene.getSpeaker()))
                        .findFirst()
                        .orElse(null);
                
                if (matchedCharacter != null && !matchedCharacter.getId().equals(scene.getCharacterId())) {
                    // 更新角色关联
                    chapterSceneMapper.updateCharacterLink(scene.getId(), matchedCharacter.getId());
                    linkedCount++;
                    log.debug("自动关联角色成功: 分镜ID={}, 角色={}", scene.getId(), matchedCharacter.getName());
                }
            }
        }
        
        log.info("自动关联角色完成，章节ID: {}, 关联数量: {}", chapterId, linkedCount);
        return linkedCount;
    }

    /**
     * 验证小说所有权
     */
    private void validateNovelOwnership(Long novelId, Long userId) {
        // 这里应该调用NovelService来验证，简化实现
        if (novelId == null) {
            throw new BusinessException("小说ID不能为空");
        }
    }

    /**
     * 验证章节所有权
     */
    private void validateChapterOwnership(Long chapterId, Long userId) {
        NovelChapter chapter = novelChapterMapper.findById(chapterId);
        if (chapter == null) {
            throw new BusinessException("章节不存在");
        }
        
        // 这里应该进一步验证用户对小说的权限
        validateNovelOwnership(chapter.getNovelId(), userId);
    }
}
