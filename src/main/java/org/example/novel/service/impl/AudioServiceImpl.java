package org.example.novel.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.converter.AudioConverter;
import org.example.novel.dto.AudioDTO;
import org.example.novel.dto.CreateAudioRequestDTO;
import org.example.novel.dto.UpdateAudioRequestDTO;
import org.example.novel.entity.Audio;
import org.example.novel.exception.BusinessException;
import org.example.novel.mapper.AudioMapper;
import org.example.novel.service.AudioService;
import org.example.novel.service.FileResourceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 音频素材服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AudioServiceImpl implements AudioService {

    private final AudioMapper audioMapper;
    private final AudioConverter audioConverter;
    private final FileResourceService fileResourceService;

    @Override
    @Transactional
    public AudioDTO createAudio(CreateAudioRequestDTO createRequest, Long userId) {
        // 验证音频文件是否存在
        validateAudioFile(createRequest.getAudioUuid());

        // 转换为实体
        Audio audio = audioConverter.toEntity(createRequest);
        audio.setUserId(userId);

        // 保存到数据库
        audioMapper.insert(audio);
        log.info("创建音频素材成功，ID: {}, 用户ID: {}", audio.getId(), userId);

        // 转换为DTO并设置音频URL
        AudioDTO audioDTO = audioConverter.toDTO(audio);
        setAudioUrl(audioDTO);
        
        return audioDTO;
    }

    @Override
    @Transactional
    public AudioDTO updateAudio(Long id, UpdateAudioRequestDTO updateRequest, Long userId) {
        // 检查音频是否存在且属于当前用户
        Audio existingAudio = getAudioByIdAndUserId(id, userId);

        // 如果更新了音频UUID，验证新音频文件是否存在
        if (StringUtils.hasText(updateRequest.getAudioUuid())) {
            validateAudioFile(updateRequest.getAudioUuid());
        }

        // 更新实体
        audioConverter.updateEntity(updateRequest, existingAudio);

        // 保存到数据库
        audioMapper.update(existingAudio);
        log.info("更新音频素材成功，ID: {}, 用户ID: {}", id, userId);

        // 转换为DTO并设置音频URL
        AudioDTO audioDTO = audioConverter.toDTO(existingAudio);
        setAudioUrl(audioDTO);
        
        return audioDTO;
    }

    @Override
    @Transactional
    public boolean deleteAudio(Long id, Long userId) {
        // 检查音频是否存在且属于当前用户
        getAudioByIdAndUserId(id, userId);

        // 删除音频
        int result = audioMapper.deleteById(id);
        log.info("删除音频素材，ID: {}, 用户ID: {}, 结果: {}", id, userId, result > 0);
        
        return result > 0;
    }

    @Override
    public AudioDTO getAudioById(Long id, Long userId) {
        Audio audio = getAudioByIdAndUserId(id, userId);
        
        AudioDTO audioDTO = audioConverter.toDTO(audio);
        setAudioUrl(audioDTO);
        
        return audioDTO;
    }

    @Override
    public List<AudioDTO> getUserAudios(Long userId, String name, String voiceType, Integer page, Integer size) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        int offset = (page - 1) * size;
        List<Audio> audios = audioMapper.findByUserId(userId, name, voiceType, offset, size);
        List<AudioDTO> audioDTOs = audioConverter.toDTOList(audios);
        
        // 为每个音频设置URL
        audioDTOs.forEach(this::setAudioUrl);
        
        return audioDTOs;
    }

    @Override
    public Long countUserAudios(Long userId, String name, String voiceType) {
        return audioMapper.countByUserId(userId, name, voiceType);
    }

    @Override
    public List<AudioDTO> getAllAudios(String name, String voiceType, Integer page, Integer size) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        int offset = (page - 1) * size;
        List<Audio> audios = audioMapper.findAll(name, voiceType, offset, size);
        List<AudioDTO> audioDTOs = audioConverter.toDTOList(audios);
        
        // 为每个音频设置URL
        audioDTOs.forEach(this::setAudioUrl);
        
        return audioDTOs;
    }

    @Override
    public Long countAllAudios(String name, String voiceType) {
        return audioMapper.countAll(name, voiceType);
    }

    /**
     * 获取音频并验证所有权
     */
    private Audio getAudioByIdAndUserId(Long id, Long userId) {
        Audio audio = audioMapper.findById(id);
        if (audio == null) {
            throw new BusinessException("音频素材不存在");
        }
        
        if (!audio.getUserId().equals(userId)) {
            throw new BusinessException("无权访问该音频素材");
        }
        
        return audio;
    }

    /**
     * 验证音频文件是否存在
     */
    private void validateAudioFile(String audioUuid) {
        try {
            fileResourceService.getByUuid(audioUuid);
        } catch (Exception e) {
            throw new BusinessException("音频文件不存在或无效");
        }
    }

    /**
     * 设置音频URL
     */
    private void setAudioUrl(AudioDTO audioDTO) {
        if (audioDTO != null && StringUtils.hasText(audioDTO.getAudioUuid())) {
            try {
                String audioUrl = fileResourceService.getPreviewUrl(audioDTO.getAudioUuid());
                audioDTO.setAudioUrl(audioUrl);
            } catch (Exception e) {
                log.warn("获取音频URL失败，UUID: {}, 错误: {}", audioDTO.getAudioUuid(), e.getMessage());
            }
        }
    }
}
