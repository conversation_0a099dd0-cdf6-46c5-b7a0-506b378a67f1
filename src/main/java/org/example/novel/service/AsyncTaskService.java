package org.example.novel.service;

import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.entity.AsyncTask;
import org.example.novel.enums.TaskStatus;

import java.util.List;

/**
 * 异步任务管理服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface AsyncTaskService {

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 任务状态
     */
    <T> AsyncTaskResultDTO<T> getTaskStatus(String taskId, Long userId);

    /**
     * 保存任务状态
     *
     * @param taskResult 任务结果
     * @param userId 用户ID
     */
    <T> void saveTaskStatus(AsyncTaskResultDTO<T> taskResult, Long userId);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param progress 进度
     * @param currentStep 当前步骤
     * @param userId 用户ID
     */
    void updateTaskProgress(String taskId, Integer progress, String currentStep, Long userId);

    /**
     * 标记任务完成
     *
     * @param taskId 任务ID
     * @param result 任务结果
     * @param userId 用户ID
     */
    <T> void completeTask(String taskId, T result, Long userId);

    /**
     * 标记任务失败
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @param userId 用户ID
     */
    void failTask(String taskId, String errorMessage, Long userId);

    /**
     * 获取用户的任务列表
     *
     * @param userId 用户ID
     * @param taskType 任务类型（可选）
     * @param status 任务状态（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 任务列表
     */
    List<AsyncTask> getUserTasks(Long userId, String taskType, TaskStatus status, Integer page, Integer size);

    /**
     * 统计用户的任务数量
     *
     * @param userId 用户ID
     * @param taskType 任务类型（可选）
     * @param status 任务状态（可选）
     * @return 任务数量
     */
    Long countUserTasks(Long userId, String taskType, TaskStatus status);

    /**
     * 清理过期任务
     */
    void cleanupExpiredTasks();
}
