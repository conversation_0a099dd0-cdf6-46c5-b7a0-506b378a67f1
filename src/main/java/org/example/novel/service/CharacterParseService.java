package org.example.novel.service;

import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.dto.CharacterParseRequestDTO;
import org.example.novel.dto.CharacterParseResponseDTO;

import java.util.List;

/**
 * 角色解析服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface CharacterParseService {

    /**
     * 异步解析章节中的角色
     *
     * @param parseRequest 解析请求
     * @param userId 用户ID
     * @return 任务ID
     */
    String parseCharactersAsync(CharacterParseRequestDTO parseRequest, Long userId);

    /**
     * 同步解析章节中的角色（已废弃，建议使用异步方法）
     *
     * @param parseRequest 解析请求
     * @param userId 用户ID
     * @return 解析响应
     */
    @Deprecated
    CharacterParseResponseDTO parseCharacters(CharacterParseRequestDTO parseRequest, Long userId);

    /**
     * 解析单个章节的角色
     *
     * @param novelId 小说ID
     * @param chapterId 章节ID
     * @param userId 用户ID
     * @return 解析出的角色列表
     */
    List<String> parseSingleChapter(Long novelId, Long chapterId, Long userId);

    /**
     * 使用AI解析文本中的角色
     *
     * @param content 文本内容
     * @return 角色名称列表
     */
    List<String> parseCharactersFromText(String content);
}
