package org.example.novel.service;

import org.example.novel.dto.LoginRequestDTO;
import org.example.novel.dto.LoginResponseDTO;
import org.example.novel.dto.UserDTO;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponseDTO login(LoginRequestDTO loginRequest);

    /**
     * 用户注册
     *
     * @param userDTO 用户信息
     * @return 注册结果
     */
    UserDTO register(UserDTO userDTO);
} 