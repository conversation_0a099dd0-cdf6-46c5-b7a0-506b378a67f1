package org.example.novel.service;

import org.example.novel.dto.CreateRoleRequestDTO;
import org.example.novel.dto.RoleDTO;
import org.example.novel.dto.UpdateRoleRequestDTO;

import java.util.List;

/**
 * 角色素材服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface RoleService {

    /**
     * 创建角色素材
     *
     * @param createRequest 创建请求
     * @param userId 用户ID
     * @return 角色素材DTO
     */
    RoleDTO createRole(CreateRoleRequestDTO createRequest, Long userId);

    /**
     * 更新角色素材
     *
     * @param id 角色ID
     * @param updateRequest 更新请求
     * @param userId 用户ID
     * @return 角色素材DTO
     */
    RoleDTO updateRole(Long id, UpdateRoleRequestDTO updateRequest, Long userId);

    /**
     * 删除角色素材
     *
     * @param id 角色ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteRole(Long id, Long userId);

    /**
     * 获取角色素材详情
     *
     * @param id 角色ID
     * @param userId 用户ID
     * @return 角色素材DTO
     */
    RoleDTO getRoleById(Long id, Long userId);

    /**
     * 获取用户的角色素材列表
     *
     * @param userId 用户ID
     * @param name 角色名称（可选，模糊查询）
     * @param tags 标签（可选，模糊查询）
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 角色素材列表
     */
    List<RoleDTO> getUserRoles(Long userId, String name, String tags, Integer page, Integer size);

    /**
     * 统计用户的角色素材数量
     *
     * @param userId 用户ID
     * @param name 角色名称（可选，模糊查询）
     * @param tags 标签（可选，模糊查询）
     * @return 总数量
     */
    Long countUserRoles(Long userId, String name, String tags);

    /**
     * 获取所有角色素材列表（管理员用）
     *
     * @param name 角色名称（可选，模糊查询）
     * @param tags 标签（可选，模糊查询）
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 角色素材列表
     */
    List<RoleDTO> getAllRoles(String name, String tags, Integer page, Integer size);

    /**
     * 统计所有角色素材数量（管理员用）
     *
     * @param name 角色名称（可选，模糊查询）
     * @param tags 标签（可选，模糊查询）
     * @return 总数量
     */
    Long countAllRoles(String name, String tags);
}
