package org.example.novel.service;

import org.example.novel.dto.AudioDTO;
import org.example.novel.dto.CreateAudioRequestDTO;
import org.example.novel.dto.UpdateAudioRequestDTO;

import java.util.List;

/**
 * 音频素材服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface AudioService {

    /**
     * 创建音频素材
     *
     * @param createRequest 创建请求
     * @param userId 用户ID
     * @return 音频素材DTO
     */
    AudioDTO createAudio(CreateAudioRequestDTO createRequest, Long userId);

    /**
     * 更新音频素材
     *
     * @param id 音频ID
     * @param updateRequest 更新请求
     * @param userId 用户ID
     * @return 音频素材DTO
     */
    AudioDTO updateAudio(Long id, UpdateAudioRequestDTO updateRequest, Long userId);

    /**
     * 删除音频素材
     *
     * @param id 音频ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteAudio(Long id, Long userId);

    /**
     * 获取音频素材详情
     *
     * @param id 音频ID
     * @param userId 用户ID
     * @return 音频素材DTO
     */
    AudioDTO getAudioById(Long id, Long userId);

    /**
     * 获取用户的音频素材列表
     *
     * @param userId 用户ID
     * @param name 音频名称（可选，模糊查询）
     * @param voiceType 音色类型（可选）
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 音频素材列表
     */
    List<AudioDTO> getUserAudios(Long userId, String name, String voiceType, Integer page, Integer size);

    /**
     * 统计用户的音频素材数量
     *
     * @param userId 用户ID
     * @param name 音频名称（可选，模糊查询）
     * @param voiceType 音色类型（可选）
     * @return 总数量
     */
    Long countUserAudios(Long userId, String name, String voiceType);

    /**
     * 获取所有音频素材列表（管理员用）
     *
     * @param name 音频名称（可选，模糊查询）
     * @param voiceType 音色类型（可选）
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 音频素材列表
     */
    List<AudioDTO> getAllAudios(String name, String voiceType, Integer page, Integer size);

    /**
     * 统计所有音频素材数量（管理员用）
     *
     * @param name 音频名称（可选，模糊查询）
     * @param voiceType 音色类型（可选）
     * @return 总数量
     */
    Long countAllAudios(String name, String voiceType);
}
