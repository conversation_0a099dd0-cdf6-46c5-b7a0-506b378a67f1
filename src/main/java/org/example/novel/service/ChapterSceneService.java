package org.example.novel.service;

import org.example.novel.dto.ChapterSceneDTO;
import org.example.novel.dto.SceneParseRequestDTO;
import org.example.novel.dto.SceneParseResponseDTO;

import java.util.List;

/**
 * 章节分镜管理服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface ChapterSceneService {

    /**
     * 异步解析并创建分镜
     *
     * @param parseRequest 解析请求
     * @param userId 用户ID
     * @return 任务ID
     */
    String parseAndCreateScenesAsync(SceneParseRequestDTO parseRequest, Long userId);

    /**
     * 同步解析并创建分镜（已废弃）
     *
     * @param parseRequest 解析请求
     * @param userId 用户ID
     * @return 解析响应
     */
    @Deprecated
    SceneParseResponseDTO parseAndCreateScenes(SceneParseRequestDTO parseRequest, Long userId);

    /**
     * 获取章节的分镜列表
     *
     * @param chapterId 章节ID
     * @param userId 用户ID
     * @return 分镜列表
     */
    List<ChapterSceneDTO> getChapterScenes(Long chapterId, Long userId);

    /**
     * 获取分镜详情
     *
     * @param sceneId 分镜ID
     * @param userId 用户ID
     * @return 分镜详情
     */
    ChapterSceneDTO getSceneDetail(Long sceneId, Long userId);

    /**
     * 更新分镜信息
     *
     * @param sceneId 分镜ID
     * @param sceneDTO 分镜信息
     * @param userId 用户ID
     * @return 更新后的分镜信息
     */
    ChapterSceneDTO updateScene(Long sceneId, ChapterSceneDTO sceneDTO, Long userId);

    /**
     * 删除分镜
     *
     * @param sceneId 分镜ID
     * @param userId 用户ID
     */
    void deleteScene(Long sceneId, Long userId);

    /**
     * 关联分镜到角色
     *
     * @param sceneId 分镜ID
     * @param characterId 角色ID
     * @param userId 用户ID
     * @return 更新后的分镜信息
     */
    ChapterSceneDTO linkCharacter(Long sceneId, Long characterId, Long userId);

    /**
     * 自动关联分镜中的说话者到角色
     *
     * @param chapterId 章节ID
     * @param userId 用户ID
     * @return 关联成功的分镜数量
     */
    Integer autoLinkCharacters(Long chapterId, Long userId);
}
