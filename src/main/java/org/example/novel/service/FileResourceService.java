package org.example.novel.service;

import org.example.novel.dto.FileResourceDTO;
import org.example.novel.dto.MD5CheckRequestDTO;
import org.example.novel.dto.MD5CheckResponseDTO;
import org.example.novel.entity.FileResource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件资源服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface FileResourceService {

    /**
     * 检查文件MD5是否已存在
     *
     * @param request MD5检查请求
     * @return MD5检查响应
     */
    MD5CheckResponseDTO checkMD5(MD5CheckRequestDTO request);

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件资源DTO
     */
    FileResourceDTO uploadFile(MultipartFile file);

    /**
     * 通过UUID获取文件资源
     *
     * @param uuid 文件UUID
     * @return 文件资源DTO
     */
    FileResourceDTO getByUuid(String uuid);

    /**
     * 通过ID获取文件资源
     *
     * @param id 资源ID
     * @return 文件资源DTO
     */
    FileResourceDTO getById(Long id);

    /**
     * 获取文件列表
     *
     * @param bucket   存储桶（可选）
     * @param mimeType MIME类型（可选）
     * @param page     页码（从1开始）
     * @param size     每页大小
     * @return 文件资源列表
     */
    List<FileResourceDTO> getFileList(String bucket, String mimeType, Integer page, Integer size);

    /**
     * 统计文件数量
     *
     * @param bucket   存储桶（可选）
     * @param mimeType MIME类型（可选）
     * @return 文件数量
     */
    Long countFiles(String bucket, String mimeType);

    /**
     * 获取文件下载URL
     *
     * @param uuid 文件UUID
     * @return 下载URL
     */
    String getDownloadUrl(String uuid);

    /**
     * 获取文件预览URL
     *
     * @param uuid 文件UUID
     * @return 预览URL
     */
    String getPreviewUrl(String uuid);

    /**
     * 删除文件
     *
     * @param uuid 文件UUID
     * @return 是否删除成功
     */
    boolean deleteFile(String uuid);

    /**
     * 通过MD5获取文件资源
     *
     * @param md5 文件MD5值
     * @return 文件资源
     */
    FileResource getByMd5(String md5);
}
