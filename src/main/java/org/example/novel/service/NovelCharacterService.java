package org.example.novel.service;

import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.dto.CharacterParseRequestDTO;
import org.example.novel.dto.CharacterParseResponseDTO;
import org.example.novel.dto.NovelCharacterDTO;

import java.util.List;

/**
 * 小说角色管理服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface NovelCharacterService {

    /**
     * 异步解析章节角色并创建角色记录
     *
     * @param parseRequest 解析请求
     * @param userId 用户ID
     * @return 任务ID
     */
    String parseAndCreateCharactersAsync(CharacterParseRequestDTO parseRequest, Long userId);

    /**
     * 同步解析章节角色并创建角色记录（已废弃）
     *
     * @param parseRequest 解析请求
     * @param userId 用户ID
     * @return 解析响应
     */
    @Deprecated
    CharacterParseResponseDTO parseAndCreateCharacters(CharacterParseRequestDTO parseRequest, Long userId);

    /**
     * 手动创建角色
     *
     * @param novelId 小说ID
     * @param name 角色名称
     * @param description 角色描述
     * @param userId 用户ID
     * @return 角色DTO
     */
    NovelCharacterDTO createCharacter(Long novelId, String name, String description, Long userId);

    /**
     * 更新角色信息
     *
     * @param characterId 角色ID
     * @param name 角色名称
     * @param description 角色描述
     * @param userId 用户ID
     * @return 角色DTO
     */
    NovelCharacterDTO updateCharacter(Long characterId, String name, String description, Long userId);

    /**
     * 关联角色素材
     *
     * @param characterId 角色ID
     * @param roleId 角色素材ID
     * @param userId 用户ID
     * @return 角色DTO
     */
    NovelCharacterDTO linkRoleMaterial(Long characterId, Long roleId, Long userId);

    /**
     * 关联音频素材
     *
     * @param characterId 角色ID
     * @param audioId 音频素材ID
     * @param userId 用户ID
     * @return 角色DTO
     */
    NovelCharacterDTO linkAudioMaterial(Long characterId, Long audioId, Long userId);

    /**
     * 删除角色
     *
     * @param characterId 角色ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteCharacter(Long characterId, Long userId);

    /**
     * 获取角色详情
     *
     * @param characterId 角色ID
     * @param userId 用户ID
     * @return 角色DTO
     */
    NovelCharacterDTO getCharacterById(Long characterId, Long userId);

    /**
     * 获取小说的角色列表
     *
     * @param novelId 小说ID
     * @param userId 用户ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 角色列表
     */
    List<NovelCharacterDTO> getNovelCharacters(Long novelId, Long userId, Integer page, Integer size);

    /**
     * 统计小说的角色数量
     *
     * @param novelId 小说ID
     * @param userId 用户ID
     * @return 角色数量
     */
    Long countNovelCharacters(Long novelId, Long userId);

    /**
     * 自动关联角色素材
     *
     * @param characterId 角色ID
     * @param userId 用户ID
     * @return 是否关联成功
     */
    boolean autoLinkRoleMaterial(Long characterId, Long userId);
}
