package org.example.novel.service;

import org.example.novel.dto.CreateNovelRequestDTO;
import org.example.novel.dto.NovelDTO;
import org.example.novel.dto.UpdateNovelRequestDTO;
import org.example.novel.dto.UploadNovelRequestDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 小说服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface NovelService {

    /**
     * 创建小说
     *
     * @param createRequest 创建请求
     * @param userId 用户ID
     * @return 小说DTO
     */
    NovelDTO createNovel(CreateNovelRequestDTO createRequest, Long userId);

    /**
     * 上传小说文件并创建小说
     *
     * @param file 小说文件
     * @param uploadRequest 上传请求
     * @param userId 用户ID
     * @return 小说DTO
     */
    NovelDTO uploadNovel(MultipartFile file, UploadNovelRequestDTO uploadRequest, Long userId);

    /**
     * 更新小说
     *
     * @param id 小说ID
     * @param updateRequest 更新请求
     * @param userId 用户ID
     * @return 小说DTO
     */
    NovelDTO updateNovel(Long id, UpdateNovelRequestDTO updateRequest, Long userId);

    /**
     * 删除小说
     *
     * @param id 小说ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteNovel(Long id, Long userId);

    /**
     * 获取小说详情
     *
     * @param id 小说ID
     * @param userId 用户ID
     * @return 小说DTO
     */
    NovelDTO getNovelById(Long id, Long userId);

    /**
     * 获取用户的小说列表
     *
     * @param userId 用户ID
     * @param title 小说标题（可选，模糊查询）
     * @param author 作者（可选，模糊查询）
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 小说列表
     */
    List<NovelDTO> getUserNovels(Long userId, String title, String author, Integer page, Integer size);

    /**
     * 统计用户的小说数量
     *
     * @param userId 用户ID
     * @param title 小说标题（可选，模糊查询）
     * @param author 作者（可选，模糊查询）
     * @return 总数量
     */
    Long countUserNovels(Long userId, String title, String author);

    /**
     * 获取所有小说列表（管理员用）
     *
     * @param title 小说标题（可选，模糊查询）
     * @param author 作者（可选，模糊查询）
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 小说列表
     */
    List<NovelDTO> getAllNovels(String title, String author, Integer page, Integer size);

    /**
     * 统计所有小说数量（管理员用）
     *
     * @param title 小说标题（可选，模糊查询）
     * @param author 作者（可选，模糊查询）
     * @return 总数量
     */
    Long countAllNovels(String title, String author);
}
