package org.example.novel.service;

/**
 * AI客户端服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface AIClientService {

    /**
     * 调用Gemini API
     *
     * @param prompt 提示词
     * @return AI响应
     */
    String callGemini(String prompt);

    /**
     * 调用Ollama API
     *
     * @param prompt 提示词
     * @return AI响应
     */
    String callOllama(String prompt);

    /**
     * 调用默认AI服务
     *
     * @param prompt 提示词
     * @return AI响应
     */
    String callDefault(String prompt);

    /**
     * 调用指定的AI服务
     *
     * @param provider AI服务提供商（gemini或ollama）
     * @param prompt 提示词
     * @return AI响应
     */
    String call(String provider, String prompt);

    /**
     * 测试Gemini连接
     *
     * @return 是否连接成功
     */
    boolean testGeminiConnection();

    /**
     * 测试Ollama连接
     *
     * @return 是否连接成功
     */
    boolean testOllamaConnection();
}
