package org.example.novel.service;

import org.example.novel.dto.SceneParseRequestDTO;
import org.example.novel.dto.SceneParseResponseDTO;

/**
 * 分镜解析服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public interface SceneParseService {

    /**
     * 异步解析章节分镜
     *
     * @param parseRequest 解析请求
     * @param userId 用户ID
     * @return 任务ID
     */
    String parseSceneAsync(SceneParseRequestDTO parseRequest, Long userId);

    /**
     * 同步解析章节分镜（已废弃，建议使用异步方法）
     *
     * @param parseRequest 解析请求
     * @param userId 用户ID
     * @return 解析响应
     */
    @Deprecated
    SceneParseResponseDTO parseScene(SceneParseRequestDTO parseRequest, Long userId);

    /**
     * 解析单个章节的分镜
     *
     * @param novelId 小说ID
     * @param chapterId 章节ID
     * @param userId 用户ID
     * @return 分镜列表（JSON格式）
     */
    String parseSingleChapterScene(Long novelId, Long chapterId, Long userId);
}
