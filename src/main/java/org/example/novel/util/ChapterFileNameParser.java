package org.example.novel.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 章节文件名解析工具
 * 智能解析txt文件名，提取章节序号和标题
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
public class ChapterFileNameParser {

    /**
     * 解析结果
     */
    @Data
    public static class ParseResult {
        private Integer chapterIndex;
        private String chapterTitle;
        private String originalFileName;
        private boolean success;
    }

    // 中文数字映射
    private static final Map<Character, Integer> CHINESE_NUMBER_MAP = new HashMap<>();
    
    static {
        CHINESE_NUMBER_MAP.put('零', 0);
        CHINESE_NUMBER_MAP.put('一', 1);
        CHINESE_NUMBER_MAP.put('二', 2);
        CHINESE_NUMBER_MAP.put('三', 3);
        CHINESE_NUMBER_MAP.put('四', 4);
        CHINESE_NUMBER_MAP.put('五', 5);
        CHINESE_NUMBER_MAP.put('六', 6);
        CHINESE_NUMBER_MAP.put('七', 7);
        CHINESE_NUMBER_MAP.put('八', 8);
        CHINESE_NUMBER_MAP.put('九', 9);
        CHINESE_NUMBER_MAP.put('十', 10);
        CHINESE_NUMBER_MAP.put('百', 100);
        CHINESE_NUMBER_MAP.put('千', 1000);
        CHINESE_NUMBER_MAP.put('万', 10000);
    }

    // 文件名解析正则表达式模式
    private static final Pattern[] FILE_NAME_PATTERNS = {
        // 第X章 标题.txt
        Pattern.compile("^第([一二三四五六七八九十百千万\\d]+)章\\s*(.*)\\.(txt|TXT)$"),
        // 第X回 标题.txt
        Pattern.compile("^第([一二三四五六七八九十百千万\\d]+)回\\s*(.*)\\.(txt|TXT)$"),
        // 第X节 标题.txt
        Pattern.compile("^第([一二三四五六七八九十百千万\\d]+)节\\s*(.*)\\.(txt|TXT)$"),
        // Chapter X 标题.txt
        Pattern.compile("^Chapter\\s*([\\d]+)\\s*(.*)\\.(txt|TXT)$", Pattern.CASE_INSENSITIVE),
        // 章节X 标题.txt
        Pattern.compile("^章节([一二三四五六七八九十百千万\\d]+)\\s*(.*)\\.(txt|TXT)$"),
        // X. 标题.txt
        Pattern.compile("^(\\d+)\\.\\s*(.+)\\.(txt|TXT)$"),
        // X 标题.txt (纯数字开头)
        Pattern.compile("^(\\d+)\\s+(.+)\\.(txt|TXT)$"),
        // X-标题.txt
        Pattern.compile("^(\\d+)-(.+)\\.(txt|TXT)$"),
        // X_标题.txt
        Pattern.compile("^(\\d+)_(.+)\\.(txt|TXT)$")
    };

    /**
     * 解析文件名
     *
     * @param fileName 文件名
     * @return 解析结果
     */
    public static ParseResult parseFileName(String fileName) {
        ParseResult result = new ParseResult();
        result.setOriginalFileName(fileName);
        result.setSuccess(false);

        if (fileName == null || fileName.trim().isEmpty()) {
            return result;
        }

        // 尝试不同的模式
        for (Pattern pattern : FILE_NAME_PATTERNS) {
            Matcher matcher = pattern.matcher(fileName.trim());
            if (matcher.matches()) {
                String chapterNumberStr = matcher.group(1);
                String titlePart = matcher.groupCount() >= 2 ? matcher.group(2) : "";

                // 解析章节号
                Integer chapterIndex = parseChapterNumber(chapterNumberStr);
                if (chapterIndex != null && chapterIndex > 0) {
                    result.setChapterIndex(chapterIndex);
                    
                    // 处理标题
                    String title = titlePart.trim();
                    if (title.isEmpty()) {
                        title = "第" + chapterIndex + "章";
                    }
                    result.setChapterTitle(title);
                    result.setSuccess(true);
                    
                    log.debug("成功解析文件名: {} -> 章节{}: {}", fileName, chapterIndex, title);
                    return result;
                }
            }
        }

        // 如果所有模式都不匹配，尝试提取纯文件名作为标题
        String nameWithoutExt = fileName;
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            nameWithoutExt = fileName.substring(0, lastDotIndex);
        }
        
        result.setChapterTitle(nameWithoutExt);
        result.setChapterIndex(null); // 无法确定章节号
        result.setSuccess(false);
        
        log.warn("无法解析文件名中的章节号: {}", fileName);
        return result;
    }

    /**
     * 解析章节号（支持中文数字和阿拉伯数字）
     */
    private static Integer parseChapterNumber(String numberStr) {
        if (numberStr == null || numberStr.trim().isEmpty()) {
            return null;
        }

        numberStr = numberStr.trim();

        // 尝试解析阿拉伯数字
        try {
            return Integer.parseInt(numberStr);
        } catch (NumberFormatException e) {
            // 继续尝试中文数字
        }

        // 解析中文数字
        return parseChineseNumber(numberStr);
    }

    /**
     * 解析中文数字
     */
    private static Integer parseChineseNumber(String chineseNumber) {
        if (chineseNumber == null || chineseNumber.isEmpty()) {
            return null;
        }

        try {
            int result = 0;
            int temp = 0;
            int unit = 1;

            for (int i = chineseNumber.length() - 1; i >= 0; i--) {
                char c = chineseNumber.charAt(i);
                Integer value = CHINESE_NUMBER_MAP.get(c);
                
                if (value == null) {
                    continue; // 跳过不认识的字符
                }

                if (value >= 10) {
                    if (value > unit) {
                        unit = value;
                        if (temp == 0) {
                            temp = 1;
                        }
                    } else {
                        result += temp * unit;
                        temp = 0;
                        unit = value;
                    }
                } else {
                    temp += value;
                }
            }

            result += temp * unit;
            return result > 0 ? result : null;
        } catch (Exception e) {
            log.warn("解析中文数字失败: {}", chineseNumber, e);
            return null;
        }
    }

    /**
     * 批量解析文件名
     *
     * @param fileNames 文件名列表
     * @return 解析结果列表
     */
    public static Map<String, ParseResult> batchParseFileNames(String[] fileNames) {
        Map<String, ParseResult> results = new HashMap<>();
        
        if (fileNames != null) {
            for (String fileName : fileNames) {
                ParseResult result = parseFileName(fileName);
                results.put(fileName, result);
            }
        }
        
        return results;
    }

    /**
     * 验证解析结果
     *
     * @param results 解析结果
     * @return 是否有效
     */
    public static boolean validateParseResults(Map<String, ParseResult> results) {
        if (results == null || results.isEmpty()) {
            return false;
        }

        // 检查是否有重复的章节号
        Map<Integer, String> chapterIndexMap = new HashMap<>();
        
        for (Map.Entry<String, ParseResult> entry : results.entrySet()) {
            ParseResult result = entry.getValue();
            if (result.isSuccess() && result.getChapterIndex() != null) {
                Integer chapterIndex = result.getChapterIndex();
                if (chapterIndexMap.containsKey(chapterIndex)) {
                    log.warn("发现重复的章节号: {} (文件: {} 和 {})", 
                            chapterIndex, chapterIndexMap.get(chapterIndex), entry.getKey());
                    return false;
                }
                chapterIndexMap.put(chapterIndex, entry.getKey());
            }
        }

        return true;
    }
}
