package org.example.novel.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;

/**
 * 代理测试工具类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
public class ProxyTestUtil {

    /**
     * 测试代理连接
     *
     * @param webClient 配置了代理的WebClient
     * @param testUrl 测试URL
     * @return 是否连接成功
     */
    public static boolean testProxyConnection(WebClient webClient, String testUrl) {
        try {
            log.info("测试代理连接，目标URL: {}", testUrl);
            
            String response = webClient.get()
                    .uri(testUrl)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();
            
            log.info("代理连接测试成功");
            return true;
            
        } catch (WebClientResponseException e) {
            log.warn("代理连接测试失败，HTTP状态码: {}, 响应: {}", 
                    e.getStatusCode(), e.getResponseBodyAsString());
            return false;
        } catch (Exception e) {
            log.warn("代理连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 测试Gemini API连接
     *
     * @param webClient 配置了代理的WebClient
     * @param apiKey Gemini API密钥
     * @return 是否连接成功
     */
    public static boolean testGeminiConnection(WebClient webClient, String apiKey) {
        String testUrl = "https://generativelanguage.googleapis.com/v1beta/models?key=" + apiKey;
        return testProxyConnection(webClient, testUrl);
    }
}
