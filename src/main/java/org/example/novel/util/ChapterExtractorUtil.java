package org.example.novel.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 智能章节提取工具类
 * 支持中文数字和阿拉伯数字的章节标题识别
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
public class ChapterExtractorUtil {

    /**
     * 章节信息
     */
    @Data
    public static class ChapterInfo {
        private String title;
        private String content;
        private Integer chapterIndex;
        private Integer wordCount;
    }

    // 中文数字映射
    private static final Map<Character, Integer> CHINESE_NUMBER_MAP = new HashMap<>();
    
    static {
        CHINESE_NUMBER_MAP.put('零', 0);
        CHINESE_NUMBER_MAP.put('一', 1);
        CHINESE_NUMBER_MAP.put('二', 2);
        CHINESE_NUMBER_MAP.put('三', 3);
        CHINESE_NUMBER_MAP.put('四', 4);
        CHINESE_NUMBER_MAP.put('五', 5);
        CHINESE_NUMBER_MAP.put('六', 6);
        CHINESE_NUMBER_MAP.put('七', 7);
        CHINESE_NUMBER_MAP.put('八', 8);
        CHINESE_NUMBER_MAP.put('九', 9);
        CHINESE_NUMBER_MAP.put('十', 10);
        CHINESE_NUMBER_MAP.put('百', 100);
        CHINESE_NUMBER_MAP.put('千', 1000);
        CHINESE_NUMBER_MAP.put('万', 10000);
    }

    // 章节标题正则表达式模式
    private static final List<Pattern> CHAPTER_PATTERNS = Arrays.asList(
        // 第X章 格式
        Pattern.compile("^\\s*第([一二三四五六七八九十百千万\\d]+)章\\s*(.*)$", Pattern.MULTILINE),
        // 第X回 格式
        Pattern.compile("^\\s*第([一二三四五六七八九十百千万\\d]+)回\\s*(.*)$", Pattern.MULTILINE),
        // 第X节 格式
        Pattern.compile("^\\s*第([一二三四五六七八九十百千万\\d]+)节\\s*(.*)$", Pattern.MULTILINE),
        // Chapter X 格式
        Pattern.compile("^\\s*Chapter\\s*([\\d]+)\\s*(.*)$", Pattern.MULTILINE | Pattern.CASE_INSENSITIVE),
        // 章节X 格式
        Pattern.compile("^\\s*章节([一二三四五六七八九十百千万\\d]+)\\s*(.*)$", Pattern.MULTILINE),
        // X. 格式（简单数字标题）
        Pattern.compile("^\\s*(\\d+)\\.\\s*(.+)$", Pattern.MULTILINE)
    );

    /**
     * 从文本中提取章节
     *
     * @param content 小说文本内容
     * @return 章节列表
     */
    public static List<ChapterInfo> extractChapters(String content) {
        if (content == null || content.trim().isEmpty()) {
            return Collections.emptyList();
        }

        List<ChapterInfo> chapters = new ArrayList<>();
        
        // 尝试不同的章节模式
        for (Pattern pattern : CHAPTER_PATTERNS) {
            chapters = tryExtractWithPattern(content, pattern);
            if (!chapters.isEmpty()) {
                log.info("使用模式 {} 成功提取到 {} 个章节", pattern.pattern(), chapters.size());
                break;
            }
        }

        // 如果没有找到章节，将整个内容作为一个章节
        if (chapters.isEmpty()) {
            log.warn("未能识别章节结构，将整个内容作为单一章节");
            ChapterInfo singleChapter = new ChapterInfo();
            singleChapter.setTitle("正文");
            singleChapter.setContent(content.trim());
            singleChapter.setChapterIndex(1);
            singleChapter.setWordCount(content.length());
            chapters.add(singleChapter);
        }

        return chapters;
    }

    /**
     * 使用指定模式尝试提取章节
     */
    private static List<ChapterInfo> tryExtractWithPattern(String content, Pattern pattern) {
        List<ChapterInfo> chapters = new ArrayList<>();
        Matcher matcher = pattern.matcher(content);
        
        List<MatchInfo> matches = new ArrayList<>();
        
        // 收集所有匹配
        while (matcher.find()) {
            MatchInfo matchInfo = new MatchInfo();
            matchInfo.start = matcher.start();
            matchInfo.end = matcher.end();
            matchInfo.chapterNumber = matcher.group(1);
            matchInfo.chapterTitle = matcher.groupCount() > 1 ? matcher.group(2) : "";
            matches.add(matchInfo);
        }

        // 如果找到匹配，提取章节内容
        if (!matches.isEmpty()) {
            for (int i = 0; i < matches.size(); i++) {
                MatchInfo current = matches.get(i);
                MatchInfo next = (i + 1 < matches.size()) ? matches.get(i + 1) : null;
                
                ChapterInfo chapter = new ChapterInfo();
                
                // 解析章节号
                Integer chapterIndex = parseChapterNumber(current.chapterNumber);
                if (chapterIndex == null) {
                    continue; // 跳过无法解析的章节号
                }
                
                chapter.setChapterIndex(chapterIndex);
                
                // 设置章节标题
                String title = current.chapterTitle.trim();
                if (title.isEmpty()) {
                    title = "第" + chapterIndex + "章";
                }
                chapter.setTitle(title);
                
                // 提取章节内容
                int contentStart = current.end;
                int contentEnd = (next != null) ? next.start : content.length();
                String chapterContent = content.substring(contentStart, contentEnd).trim();
                
                chapter.setContent(chapterContent);
                chapter.setWordCount(chapterContent.length());
                
                chapters.add(chapter);
            }
        }

        return chapters;
    }

    /**
     * 解析章节号（支持中文数字和阿拉伯数字）
     */
    private static Integer parseChapterNumber(String numberStr) {
        if (numberStr == null || numberStr.trim().isEmpty()) {
            return null;
        }

        numberStr = numberStr.trim();

        // 尝试解析阿拉伯数字
        try {
            return Integer.parseInt(numberStr);
        } catch (NumberFormatException e) {
            // 继续尝试中文数字
        }

        // 解析中文数字
        return parseChineseNumber(numberStr);
    }

    /**
     * 解析中文数字
     */
    private static Integer parseChineseNumber(String chineseNumber) {
        if (chineseNumber == null || chineseNumber.isEmpty()) {
            return null;
        }

        try {
            int result = 0;
            int temp = 0;
            int unit = 1;

            for (int i = chineseNumber.length() - 1; i >= 0; i--) {
                char c = chineseNumber.charAt(i);
                Integer value = CHINESE_NUMBER_MAP.get(c);
                
                if (value == null) {
                    continue; // 跳过不认识的字符
                }

                if (value >= 10) {
                    if (value > unit) {
                        unit = value;
                        if (temp == 0) {
                            temp = 1;
                        }
                    } else {
                        result += temp * unit;
                        temp = 0;
                        unit = value;
                    }
                } else {
                    temp += value;
                }
            }

            result += temp * unit;
            return result > 0 ? result : null;
        } catch (Exception e) {
            log.warn("解析中文数字失败: {}", chineseNumber, e);
            return null;
        }
    }

    /**
     * 匹配信息内部类
     */
    private static class MatchInfo {
        int start;
        int end;
        String chapterNumber;
        String chapterTitle;
    }

    /**
     * 验证章节提取结果
     */
    public static boolean validateChapters(List<ChapterInfo> chapters) {
        if (chapters == null || chapters.isEmpty()) {
            return false;
        }

        // 检查章节号是否连续
        Set<Integer> chapterIndexes = new HashSet<>();
        for (ChapterInfo chapter : chapters) {
            if (chapter.getChapterIndex() == null || chapter.getChapterIndex() <= 0) {
                return false;
            }
            if (chapterIndexes.contains(chapter.getChapterIndex())) {
                return false; // 重复章节号
            }
            chapterIndexes.add(chapter.getChapterIndex());
        }

        return true;
    }
}
