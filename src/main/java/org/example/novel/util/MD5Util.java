package org.example.novel.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5工具类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
public class MD5Util {

    private static final String MD5_ALGORITHM = "MD5";
    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /**
     * 计算MultipartFile的MD5值
     *
     * @param file 文件
     * @return MD5值
     */
    public static String calculateMD5(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return null;
        }
        
        try (InputStream inputStream = file.getInputStream()) {
            return calculateMD5(inputStream);
        } catch (IOException e) {
            log.error("计算文件MD5失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算输入流的MD5值
     *
     * @param inputStream 输入流
     * @return MD5值
     */
    public static String calculateMD5(InputStream inputStream) {
        if (inputStream == null) {
            return null;
        }

        try {
            MessageDigest md5 = MessageDigest.getInstance(MD5_ALGORITHM);
            byte[] buffer = new byte[8192];
            int length;
            
            while ((length = inputStream.read(buffer)) != -1) {
                md5.update(buffer, 0, length);
            }
            
            return bytesToHex(md5.digest());
        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("计算MD5失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算字符串的MD5值
     *
     * @param text 字符串
     * @return MD5值
     */
    public static String calculateMD5(String text) {
        if (text == null || text.isEmpty()) {
            return null;
        }

        try {
            MessageDigest md5 = MessageDigest.getInstance(MD5_ALGORITHM);
            md5.update(text.getBytes("UTF-8"));
            return bytesToHex(md5.digest());
        } catch (Exception e) {
            log.error("计算字符串MD5失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 字节数组转换为十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(HEX_DIGITS[(b >> 4) & 0xf]);
            result.append(HEX_DIGITS[b & 0xf]);
        }
        return result.toString();
    }

    /**
     * 验证文件MD5值
     *
     * @param file 文件
     * @param expectedMD5 期望的MD5值
     * @return 是否匹配
     */
    public static boolean verifyMD5(MultipartFile file, String expectedMD5) {
        if (file == null || expectedMD5 == null) {
            return false;
        }
        
        String actualMD5 = calculateMD5(file);
        return expectedMD5.equalsIgnoreCase(actualMD5);
    }
}
