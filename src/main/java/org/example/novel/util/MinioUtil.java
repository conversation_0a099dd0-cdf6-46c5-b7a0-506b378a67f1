package org.example.novel.util;

import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.config.MinioConfig;
import org.example.novel.exception.BusinessException;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * MinIO工具类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MinioUtil {

    private final MinioClient minioClient;
    private final MinioConfig minioConfig;

    /**
     * 创建存储桶
     *
     * @param bucketName 存储桶名称
     */
    public void createBucket(String bucketName) {
        try {
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!exists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("创建存储桶成功：{}", bucketName);
            }
        } catch (Exception e) {
            log.error("创建存储桶异常：{}", e.getMessage(), e);
            throw new BusinessException("创建存储桶失败");
        }
    }

    /**
     * 上传文件到指定存储桶
     *
     * @param file       文件
     * @param fileName   文件名
     * @param bucketName 存储桶名称
     * @return 文件UUID
     */
    public String uploadFile(MultipartFile file, String fileName, String bucketName) {
        try {
            // 确保存储桶存在
            createBucket(bucketName);
            // 生成文件名
            String objectName = generateObjectName(fileName);
            // 上传文件
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build());
            log.info("上传文件成功：{}", objectName);
            return objectName;
        } catch (Exception e) {
            log.error("上传文件异常：{}", e.getMessage(), e);
            throw new BusinessException("上传文件失败");
        }
    }

    /**
     * 上传文件（使用默认存储桶）
     *
     * @param file     文件
     * @param fileName 文件名
     * @return 文件UUID
     */
    public String uploadFile(MultipartFile file, String fileName) {
        return uploadFile(file, fileName, minioConfig.getBucket());
    }

    /**
     * 上传文件到指定存储桶
     *
     * @param inputStream 输入流
     * @param fileName    文件名
     * @param contentType 内容类型
     * @param bucketName  存储桶名称
     * @return 文件UUID
     */
    public String uploadFile(InputStream inputStream, String fileName, String contentType, String bucketName) {
        try {
            // 确保存储桶存在
            createBucket(bucketName);
            // 生成文件名
            String objectName = generateObjectName(fileName);
            // 上传文件
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, -1, 10485760)
                    .contentType(contentType)
                    .build());
            log.info("上传文件成功：{}", objectName);
            return objectName;
        } catch (Exception e) {
            log.error("上传文件异常：{}", e.getMessage(), e);
            throw new BusinessException("上传文件失败");
        }
    }

    /**
     * 上传文件（使用默认存储桶）
     *
     * @param inputStream 输入流
     * @param fileName    文件名
     * @param contentType 内容类型
     * @return 文件UUID
     */
    public String uploadFile(InputStream inputStream, String fileName, String contentType) {
        return uploadFile(inputStream, fileName, contentType, minioConfig.getBucket());
    }

    /**
     * 下载文件
     *
     * @param objectName 对象名称
     * @return 输入流
     */
    public InputStream downloadFile(String objectName) {
        try {
            return minioClient.getObject(GetObjectArgs.builder()
                    .bucket(minioConfig.getBucket())
                    .object(objectName)
                    .build());
        } catch (Exception e) {
            log.error("下载文件异常：{}", e.getMessage(), e);
            throw new BusinessException("下载文件失败");
        }
    }

    /**
     * 下载文件
     *
     * @param objectName 对象名称
     * @param bucketName 存储桶名称
     * @return 输入流
     */
    public InputStream downloadFile(String objectName,String bucketName) {
        try {
            return minioClient.getObject(GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
        } catch (Exception e) {
            log.error("下载文件异常：{}", e.getMessage(), e);
            throw new BusinessException("下载文件失败");
        }
    }

    /**
     * 获取文件访问URL
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expiry     过期时间（秒）
     * @return URL
     */
    public String getFileUrl(String bucketName, String objectName, int expiry) {
        try {
            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expiry, TimeUnit.SECONDS)
                    .build());
        } catch (Exception e) {
            log.error("获取文件访问URL异常：{}", e.getMessage(), e);
            throw new BusinessException("获取文件访问URL失败");
        }
    }

    /**
     * 获取文件访问URL（使用默认存储桶）
     *
     * @param objectName 对象名称
     * @param expiry     过期时间（秒）
     * @return URL
     */
    public String getFileUrl(String objectName, int expiry) {
        return getFileUrl(minioConfig.getBucket(), objectName, expiry);
    }

    /**
     * 删除文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     */
    public void deleteFile(String bucketName, String objectName) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
            log.info("删除文件成功：{}", objectName);
        } catch (Exception e) {
            log.error("删除文件异常：{}", e.getMessage(), e);
            throw new BusinessException("删除文件失败");
        }
    }

    /**
     * 删除文件（使用默认存储桶）
     *
     * @param objectName 对象名称
     */
    public void deleteFile(String objectName) {
        deleteFile(minioConfig.getBucket(), objectName);
    }

    /**
     * 根据MIME类型获取存储桶名称
     *
     * @param mimeType MIME类型
     * @return 存储桶名称
     */
    public String getBucketByMimeType(String mimeType) {
        if (mimeType == null) {
            return "files";
        }

        if (mimeType.startsWith("image/")) {
            return "images";
        } else if (mimeType.startsWith("audio/")) {
            return "audios";
        } else if (mimeType.startsWith("text/")) {
            return "texts";
        } else if (mimeType.startsWith("video/")) {
            return "videos";
        } else {
            return "files";
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 是否存在
     */
    public boolean fileExists(String bucketName, String objectName) {
        try {
            minioClient.statObject(StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成对象名称
     *
     * @param fileName 文件名
     * @return 对象名称
     */
    private String generateObjectName(String fileName) {
        // 获取文件后缀
        String suffix = "";
        if (fileName.contains(".")) {
            suffix = fileName.substring(fileName.lastIndexOf("."));
        }
        // 生成UUID作为文件名
        return UUID.randomUUID().toString().replace("-", "") + suffix;
    }
} 