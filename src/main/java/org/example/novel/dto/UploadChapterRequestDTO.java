package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;

/**
 * 上传章节文件请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "上传章节文件请求")
public class UploadChapterRequestDTO {

    @Size(max = 200, message = "章节标题长度不能超过200个字符")
    @Schema(description = "章节标题（可选，如果不提供则从文件名解析）")
    private String title;

    @Schema(description = "章节序号（可选，如果不提供则从文件名解析）")
    private Integer chapterIndex;

    @Schema(description = "是否覆盖已存在的章节", defaultValue = "false")
    private Boolean overwrite = false;
}
