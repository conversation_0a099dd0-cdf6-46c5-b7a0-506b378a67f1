package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 小说章节DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "小说章节信息")
public class NovelChapterDTO {

    @Schema(description = "章节ID")
    private Long id;

    @Schema(description = "小说ID")
    private Long novelId;

    @Schema(description = "章节标题")
    private String title;

    @Schema(description = "章节序号")
    private Integer chapterIndex;

    @Schema(description = "章节内容UUID")
    private String contentUuid;

    @Schema(description = "章节内容（仅在需要时返回）")
    private String content;

    @Schema(description = "字数")
    private Integer wordCount;

    @Schema(description = "是否已进行分镜解析")
    private Boolean isSceneParsed;

    @Schema(description = "是否已解析角色")
    private Boolean isCharacterParsed;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
