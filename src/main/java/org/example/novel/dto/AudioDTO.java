package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 音频素材DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "音频素材信息")
public class AudioDTO {

    @Schema(description = "音频ID")
    private Long id;

    @Schema(description = "音频名称")
    private String name;

    @Schema(description = "音频描述")
    private String description;

    @Schema(description = "音频文件UUID")
    private String audioUuid;

    @Schema(description = "音频文件URL")
    private String audioUrl;

    @Schema(description = "音频时长(秒)")
    private Integer duration;

    @Schema(description = "音色类型")
    private String voiceType;

    @Schema(description = "创建用户ID")
    private Long userId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
