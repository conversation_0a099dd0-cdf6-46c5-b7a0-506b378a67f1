package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 分镜图片DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分镜图片信息")
public class SceneImageDTO {

    @Schema(description = "图片ID")
    private Long id;

    @Schema(description = "分镜ID")
    private Long sceneId;

    @Schema(description = "图片文件URL")
    private String imageUrl;

    @Schema(description = "图片文件名")
    private String fileName;

    @Schema(description = "图片宽度")
    private Integer width;

    @Schema(description = "图片高度")
    private Integer height;

    @Schema(description = "图片大小（字节）")
    private Long fileSize;

    @Schema(description = "生成状态")
    private String status;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
