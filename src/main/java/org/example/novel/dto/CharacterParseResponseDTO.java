package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 角色解析响应DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "角色解析响应")
public class CharacterParseResponseDTO {

    @Schema(description = "解析成功的角色列表")
    private List<NovelCharacterDTO> parsedCharacters;

    @Schema(description = "解析失败的章节列表")
    private List<FailedChapter> failedChapters;

    @Schema(description = "总章节数")
    private Integer totalChapters;

    @Schema(description = "成功解析的章节数")
    private Integer successCount;

    @Schema(description = "失败的章节数")
    private Integer failedCount;

    @Schema(description = "解析出的角色总数")
    private Integer totalCharacters;

    /**
     * 解析失败的章节信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "解析失败的章节信息")
    public static class FailedChapter {
        
        @Schema(description = "章节ID")
        private Long chapterId;
        
        @Schema(description = "章节标题")
        private String chapterTitle;
        
        @Schema(description = "失败原因")
        private String reason;
        
        @Schema(description = "错误代码")
        private String errorCode;
    }
}
