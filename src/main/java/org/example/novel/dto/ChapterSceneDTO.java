package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 章节分镜DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "章节分镜信息")
public class ChapterSceneDTO {

    @Schema(description = "分镜ID")
    private Long id;

    @Schema(description = "章节ID")
    private Long chapterId;

    @Schema(description = "分镜序号")
    private Integer sceneIndex;

    @Schema(description = "分镜文本内容")
    private String content;

    @Schema(description = "AI绘画提示词")
    private String prompt;

    @Schema(description = "朗读者/说话者（null表示旁白）")
    private String speaker;

    @Schema(description = "分镜中的角色名称列表")
    private List<String> characters;

    @Schema(description = "音频信息")
    private SceneAudioDTO audioInfo;

    @Schema(description = "图片信息")
    private SceneImageDTO imageInfo;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
