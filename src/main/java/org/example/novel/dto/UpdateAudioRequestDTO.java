package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

/**
 * 更新音频素材请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "更新音频素材请求")
public class UpdateAudioRequestDTO {

    @Size(max = 100, message = "音频名称长度不能超过100个字符")
    @Schema(description = "音频名称")
    private String name;

    @Size(max = 255, message = "音频描述长度不能超过255个字符")
    @Schema(description = "音频描述")
    private String description;

    @Schema(description = "音频文件UUID")
    private String audioUuid;

    @Positive(message = "音频时长必须大于0")
    @Schema(description = "音频时长(秒)")
    private Integer duration;

    @Size(max = 50, message = "音色类型长度不能超过50个字符")
    @Schema(description = "音色类型")
    private String voiceType;
}
