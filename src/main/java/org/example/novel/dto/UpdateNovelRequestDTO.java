package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;

/**
 * 更新小说请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "更新小说请求")
public class UpdateNovelRequestDTO {

    @Size(max = 200, message = "小说名称长度不能超过200个字符")
    @Schema(description = "小说名称")
    private String title;

    @Size(max = 100, message = "作者长度不能超过100个字符")
    @Schema(description = "作者")
    private String author;

    @Schema(description = "封面图片UUID")
    private String coverUuid;

    @Schema(description = "小说简介")
    private String description;

    @Size(max = 500, message = "来源URL长度不能超过500个字符")
    @Schema(description = "来源URL")
    private String sourceUrl;
}
