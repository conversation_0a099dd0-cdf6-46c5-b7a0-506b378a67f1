package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 小说DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "小说信息")
public class NovelDTO {

    @Schema(description = "小说ID")
    private Long id;

    @Schema(description = "小说名称")
    private String title;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "封面图片UUID")
    private String coverUuid;

    @Schema(description = "封面图片URL")
    private String coverUrl;

    @Schema(description = "小说简介")
    private String description;

    @Schema(description = "来源URL")
    private String sourceUrl;

    @Schema(description = "章节数量")
    private Integer chapterCount;

    @Schema(description = "创建用户ID")
    private Long userId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
