package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 创建角色素材请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建角色素材请求")
public class CreateRoleRequestDTO {

    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    @Schema(description = "角色名称", required = true)
    private String name;

    @NotBlank(message = "角色Prompt不能为空")
    @Schema(description = "角色Prompt描述", required = true)
    private String prompt;

    @NotBlank(message = "角色图片UUID不能为空")
    @Schema(description = "角色图片UUID", required = true)
    private String imageUuid;

    @Size(max = 255, message = "标签长度不能超过255个字符")
    @Schema(description = "角色标签，多个标签用逗号分隔")
    private String tags;
}
