package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色素材DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "角色素材信息")
public class RoleDTO {

    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色Prompt描述")
    private String prompt;

    @Schema(description = "角色图片UUID")
    private String imageUuid;

    @Schema(description = "角色图片URL")
    private String imageUrl;

    @Schema(description = "角色标签，多个标签用逗号分隔")
    private String tags;

    @Schema(description = "角色标签列表")
    private List<String> tagList;

    @Schema(description = "创建用户ID")
    private Long userId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
