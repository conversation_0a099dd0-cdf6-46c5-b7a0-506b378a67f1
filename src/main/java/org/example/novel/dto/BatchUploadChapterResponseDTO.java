package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量上传章节响应DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "批量上传章节响应")
public class BatchUploadChapterResponseDTO {

    @Schema(description = "成功上传的章节列表")
    private List<NovelChapterDTO> successChapters;

    @Schema(description = "失败的文件列表")
    private List<FailedUpload> failedUploads;

    @Schema(description = "总文件数")
    private Integer totalFiles;

    @Schema(description = "成功数量")
    private Integer successCount;

    @Schema(description = "失败数量")
    private Integer failedCount;

    /**
     * 失败上传信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "失败上传信息")
    public static class FailedUpload {
        
        @Schema(description = "文件名")
        private String fileName;
        
        @Schema(description = "失败原因")
        private String reason;
        
        @Schema(description = "错误代码")
        private String errorCode;
    }
}
