package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * MD5检查请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "MD5检查请求")
public class MD5CheckRequestDTO {

    @NotBlank(message = "MD5值不能为空")
    @Schema(description = "文件MD5值", required = true)
    private String md5;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "文件大小")
    private Long size;

    @Schema(description = "文件MIME类型")
    private String mimeType;
}
