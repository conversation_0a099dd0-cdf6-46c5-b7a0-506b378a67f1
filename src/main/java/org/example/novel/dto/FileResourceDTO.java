package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文件资源DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "文件资源信息")
public class FileResourceDTO {

    @Schema(description = "资源ID")
    private Long id;

    @Schema(description = "文件UUID")
    private String uuid;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "文件MD5值")
    private String md5;

    @Schema(description = "文件大小(字节)")
    private Long size;

    @Schema(description = "文件MIME类型")
    private String mimeType;

    @Schema(description = "存储桶")
    private String bucket;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "文件访问URL")
    private String accessUrl;

    @Schema(description = "文件预览URL")
    private String previewUrl;

    @Schema(description = "文件下载URL")
    private String downloadUrl;
}
