package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

/**
 * 创建音频素材请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建音频素材请求")
public class CreateAudioRequestDTO {

    @NotBlank(message = "音频名称不能为空")
    @Size(max = 100, message = "音频名称长度不能超过100个字符")
    @Schema(description = "音频名称", required = true)
    private String name;

    @Size(max = 255, message = "音频描述长度不能超过255个字符")
    @Schema(description = "音频描述")
    private String description;

    @NotBlank(message = "音频文件UUID不能为空")
    @Schema(description = "音频文件UUID", required = true)
    private String audioUuid;

    @NotNull(message = "音频时长不能为空")
    @Positive(message = "音频时长必须大于0")
    @Schema(description = "音频时长(秒)", required = true)
    private Integer duration;

    @Size(max = 50, message = "音色类型长度不能超过50个字符")
    @Schema(description = "音色类型")
    private String voiceType;
}
