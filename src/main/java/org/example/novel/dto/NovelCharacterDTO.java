package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 小说角色DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "小说角色信息")
public class NovelCharacterDTO {

    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "小说ID")
    private Long novelId;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色描述")
    private String description;

    @Schema(description = "关联的角色素材ID")
    private Long roleId;

    @Schema(description = "关联的角色素材信息")
    private RoleDTO roleInfo;

    @Schema(description = "关联的音频素材ID")
    private Long audioId;

    @Schema(description = "关联的音频素材信息")
    private AudioDTO audioInfo;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
