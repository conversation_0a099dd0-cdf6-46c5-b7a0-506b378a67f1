package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.novel.enums.TaskStatus;

import java.time.LocalDateTime;

/**
 * 异步任务结果DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "异步任务结果")
public class AsyncTaskResultDTO<T> {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "任务状态")
    private TaskStatus status;

    @Schema(description = "任务描述")
    private String description;

    @Schema(description = "进度百分比（0-100）")
    private Integer progress;

    @Schema(description = "当前处理信息")
    private String currentStep;

    @Schema(description = "任务结果")
    private T result;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "总耗时（毫秒）")
    private Long duration;

    /**
     * 创建等待中的任务
     */
    public static <T> AsyncTaskResultDTO<T> pending(String taskId, String description) {
        return AsyncTaskResultDTO.<T>builder()
                .taskId(taskId)
                .status(TaskStatus.PENDING)
                .description(description)
                .progress(0)
                .startTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建进行中的任务
     */
    public static <T> AsyncTaskResultDTO<T> running(String taskId, String description, Integer progress, String currentStep) {
        return AsyncTaskResultDTO.<T>builder()
                .taskId(taskId)
                .status(TaskStatus.RUNNING)
                .description(description)
                .progress(progress)
                .currentStep(currentStep)
                .build();
    }

    /**
     * 创建已完成的任务
     */
    public static <T> AsyncTaskResultDTO<T> completed(String taskId, String description, T result, LocalDateTime startTime) {
        LocalDateTime endTime = LocalDateTime.now();
        return AsyncTaskResultDTO.<T>builder()
                .taskId(taskId)
                .status(TaskStatus.COMPLETED)
                .description(description)
                .progress(100)
                .result(result)
                .startTime(startTime)
                .endTime(endTime)
                .duration(java.time.Duration.between(startTime, endTime).toMillis())
                .build();
    }

    /**
     * 创建失败的任务
     */
    public static <T> AsyncTaskResultDTO<T> failed(String taskId, String description, String errorMessage, LocalDateTime startTime) {
        LocalDateTime endTime = LocalDateTime.now();
        return AsyncTaskResultDTO.<T>builder()
                .taskId(taskId)
                .status(TaskStatus.FAILED)
                .description(description)
                .errorMessage(errorMessage)
                .startTime(startTime)
                .endTime(endTime)
                .duration(java.time.Duration.between(startTime, endTime).toMillis())
                .build();
    }
}
