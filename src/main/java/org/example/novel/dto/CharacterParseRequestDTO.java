package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色解析请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "角色解析请求")
public class CharacterParseRequestDTO {

    @NotNull(message = "小说ID不能为空")
    @Schema(description = "小说ID", required = true)
    private Long novelId;

    @NotEmpty(message = "章节ID列表不能为空")
    @Schema(description = "要解析的章节ID列表", required = true)
    private List<Long> chapterIds;

    @Schema(description = "是否覆盖已存在的角色", defaultValue = "false")
    private Boolean overwrite = false;

    @Schema(description = "是否自动关联角色素材", defaultValue = "true")
    private Boolean autoLinkRole = true;
}
