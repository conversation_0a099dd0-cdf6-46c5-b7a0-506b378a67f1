package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;

/**
 * 更新角色素材请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "更新角色素材请求")
public class UpdateRoleRequestDTO {

    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色Prompt描述")
    private String prompt;

    @Schema(description = "角色图片UUID")
    private String imageUuid;

    @Size(max = 255, message = "标签长度不能超过255个字符")
    @Schema(description = "角色标签，多个标签用逗号分隔")
    private String tags;
}
