package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MD5检查响应DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "MD5检查响应")
public class MD5CheckResponseDTO {

    @Schema(description = "文件是否已存在")
    private Boolean exists;

    @Schema(description = "文件信息（如果存在）")
    private FileResourceDTO fileResource;

    @Schema(description = "提示信息")
    private String message;
}
