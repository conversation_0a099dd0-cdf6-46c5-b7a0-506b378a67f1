package org.example.novel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 分镜音频DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分镜音频信息")
public class SceneAudioDTO {

    @Schema(description = "音频ID")
    private Long id;

    @Schema(description = "分镜ID")
    private Long sceneId;

    @Schema(description = "音频文件URL")
    private String audioUrl;

    @Schema(description = "音频文件名")
    private String fileName;

    @Schema(description = "音频时长（秒）")
    private Integer duration;

    @Schema(description = "音频大小（字节）")
    private Long fileSize;

    @Schema(description = "生成状态")
    private String status;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
