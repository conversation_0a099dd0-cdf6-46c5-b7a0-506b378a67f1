package org.example.novel.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.dto.ChapterSceneDTO;
import org.example.novel.dto.SceneAudioDTO;
import org.example.novel.dto.SceneImageDTO;
import org.example.novel.entity.ChapterScene;
import org.example.novel.entity.SceneAudio;
import org.example.novel.entity.SceneImage;
import org.example.novel.mapper.SceneAudioMapper;
import org.example.novel.mapper.SceneImageMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 章节分镜转换器
 *
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChapterSceneConverter {

    private final SceneAudioMapper sceneAudioMapper;
    private final SceneImageMapper sceneImageMapper;
    private final ObjectMapper objectMapper;

    /**
     * 实体转DTO
     */
    public ChapterSceneDTO toDTO(ChapterScene scene) {
        if (scene == null) {
            return null;
        }

        ChapterSceneDTO.ChapterSceneDTOBuilder builder = ChapterSceneDTO.builder()
                .id(scene.getId())
                .chapterId(scene.getChapterId())
                .sceneIndex(scene.getSceneIndex())
                .content(scene.getContent())
                .prompt(scene.getPrompt())
                .speaker(scene.getSpeaker())
                .characters(parseCharactersList(scene.getCharacters()))
                .createTime(scene.getCreateTime());

        // 关联音频信息
        SceneAudio audio = sceneAudioMapper.findBySceneId(scene.getId());
        if (audio != null) {
            SceneAudioDTO audioDTO = toAudioDTO(audio);
            builder.audioInfo(audioDTO);
        }

        // 关联图片信息
        SceneImage image = sceneImageMapper.findBySceneId(scene.getId());
        if (image != null) {
            SceneImageDTO imageDTO = toImageDTO(image);
            builder.imageInfo(imageDTO);
        }

        return builder.build();
    }

    /**
     * DTO转实体
     */
    public ChapterScene toEntity(ChapterSceneDTO dto) {
        if (dto == null) {
            return null;
        }

        return ChapterScene.builder()
                .id(dto.getId())
                .chapterId(dto.getChapterId())
                .sceneIndex(dto.getSceneIndex())
                .content(dto.getContent())
                .prompt(dto.getPrompt())
                .speaker(dto.getSpeaker())
                .characters(serializeCharactersList(dto.getCharacters()))
                .createTime(dto.getCreateTime())
                .build();
    }

    /**
     * 解析角色列表JSON字符串
     */
    private List<String> parseCharactersList(String charactersJson) {
        if (charactersJson == null || charactersJson.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(charactersJson, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.warn("解析角色列表失败: {}", charactersJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 序列化角色列表为JSON字符串
     */
    private String serializeCharactersList(List<String> characters) {
        if (characters == null || characters.isEmpty()) {
            return "[]";
        }

        try {
            return objectMapper.writeValueAsString(characters);
        } catch (Exception e) {
            log.warn("序列化角色列表失败: {}", characters, e);
            return "[]";
        }
    }

    /**
     * 音频实体转DTO
     */
    public SceneAudioDTO toAudioDTO(SceneAudio audio) {
        if (audio == null) {
            return null;
        }

        return SceneAudioDTO.builder()
                .id(audio.getId())
                .sceneId(audio.getSceneId())
                .audioUrl(generateAudioUrl(audio.getAudioUuid()))
                .fileName(audio.getFileName())
                .duration(audio.getDuration())
                .fileSize(audio.getFileSize())
                .status(audio.getStatus())
                .errorMessage(audio.getErrorMessage())
                .createTime(audio.getCreateTime())
                .updateTime(audio.getUpdateTime())
                .build();
    }

    /**
     * 图片实体转DTO
     */
    public SceneImageDTO toImageDTO(SceneImage image) {
        if (image == null) {
            return null;
        }

        return SceneImageDTO.builder()
                .id(image.getId())
                .sceneId(image.getSceneId())
                .imageUrl(generateImageUrl(image.getImageUuid()))
                .fileName(image.getFileName())
                .width(image.getWidth())
                .height(image.getHeight())
                .fileSize(image.getFileSize())
                .status(image.getStatus())
                .errorMessage(image.getErrorMessage())
                .createTime(image.getCreateTime())
                .updateTime(image.getUpdateTime())
                .build();
    }

    /**
     * 生成音频URL
     */
    private String generateAudioUrl(String audioUuid) {
        if (audioUuid == null) {
            return null;
        }
        // 这里应该根据实际的MinIO配置生成URL
        return "/api/files/audio/" + audioUuid;
    }

    /**
     * 生成图片URL
     */
    private String generateImageUrl(String imageUuid) {
        if (imageUuid == null) {
            return null;
        }
        // 这里应该根据实际的MinIO配置生成URL
        return "/api/files/image/" + imageUuid;
    }
}
