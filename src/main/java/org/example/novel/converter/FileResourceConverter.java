package org.example.novel.converter;

import org.example.novel.dto.FileResourceDTO;
import org.example.novel.entity.FileResource;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 文件资源转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper(componentModel = "spring")
public interface FileResourceConverter {

    /**
     * 实体转换为DTO
     *
     * @param fileResource 文件资源实体
     * @return 文件资源DTO
     */
    @Mapping(target = "accessUrl", ignore = true)
    @Mapping(target = "previewUrl", ignore = true)
    @Mapping(target = "downloadUrl", ignore = true)
    FileResourceDTO toDTO(FileResource fileResource);

    /**
     * DTO转换为实体
     *
     * @param fileResourceDTO 文件资源DTO
     * @return 文件资源实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    FileResource toEntity(FileResourceDTO fileResourceDTO);

    /**
     * 实体列表转换为DTO列表
     *
     * @param fileResources 文件资源实体列表
     * @return 文件资源DTO列表
     */
    List<FileResourceDTO> toDTOList(List<FileResource> fileResources);
}
