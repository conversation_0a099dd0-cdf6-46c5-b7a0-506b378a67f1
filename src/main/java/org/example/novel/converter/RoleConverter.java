package org.example.novel.converter;

import org.example.novel.dto.CreateRoleRequestDTO;
import org.example.novel.dto.RoleDTO;
import org.example.novel.dto.UpdateRoleRequestDTO;
import org.example.novel.entity.Role;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * 角色素材转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper(componentModel = "spring")
public interface RoleConverter {

    /**
     * 实体转换为DTO
     *
     * @param role 角色素材实体
     * @return 角色素材DTO
     */
    @Mapping(target = "imageUrl", ignore = true)
    @Mapping(target = "tagList", ignore = true)
    RoleDTO toDTO(Role role);

    /**
     * 创建请求DTO转换为实体
     *
     * @param createRequest 创建请求DTO
     * @return 角色素材实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    Role toEntity(CreateRoleRequestDTO createRequest);

    /**
     * 更新实体
     *
     * @param updateRequest 更新请求DTO
     * @param role 目标实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    void updateEntity(UpdateRoleRequestDTO updateRequest, @MappingTarget Role role);

    /**
     * 实体列表转换为DTO列表
     *
     * @param roles 角色素材实体列表
     * @return 角色素材DTO列表
     */
    List<RoleDTO> toDTOList(List<Role> roles);
}
