package org.example.novel.converter;

import org.example.novel.dto.CreateNovelRequestDTO;
import org.example.novel.dto.NovelDTO;
import org.example.novel.dto.UpdateNovelRequestDTO;
import org.example.novel.dto.UploadNovelRequestDTO;
import org.example.novel.entity.Novel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * 小说转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper(componentModel = "spring")
public interface NovelConverter {

    /**
     * 实体转换为DTO
     *
     * @param novel 小说实体
     * @return 小说DTO
     */
    @Mapping(target = "coverUrl", ignore = true)
    NovelDTO toDTO(Novel novel);

    /**
     * 创建请求DTO转换为实体
     *
     * @param createRequest 创建请求DTO
     * @return 小说实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "chapterCount", constant = "0")
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    Novel toEntity(CreateNovelRequestDTO createRequest);

    /**
     * 上传请求DTO转换为实体
     *
     * @param uploadRequest 上传请求DTO
     * @return 小说实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "chapterCount", constant = "0")
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    Novel toEntity(UploadNovelRequestDTO uploadRequest);

    /**
     * 更新实体
     *
     * @param updateRequest 更新请求DTO
     * @param novel 目标实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "chapterCount", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    void updateEntity(UpdateNovelRequestDTO updateRequest, @MappingTarget Novel novel);

    /**
     * 实体列表转换为DTO列表
     *
     * @param novels 小说实体列表
     * @return 小说DTO列表
     */
    List<NovelDTO> toDTOList(List<Novel> novels);
}
