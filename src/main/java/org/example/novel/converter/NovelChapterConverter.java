package org.example.novel.converter;

import org.example.novel.dto.NovelChapterDTO;
import org.example.novel.entity.NovelChapter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 小说章节转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper(componentModel = "spring")
public interface NovelChapterConverter {

    /**
     * 实体转换为DTO
     *
     * @param chapter 章节实体
     * @return 章节DTO
     */
    @Mapping(target = "content", ignore = true)
    NovelChapterDTO toDTO(NovelChapter chapter);

    /**
     * 实体转换为DTO（包含内容）
     *
     * @param chapter 章节实体
     * @param content 章节内容
     * @return 章节DTO
     */
    @Mapping(target = "content", source = "content")
    NovelChapterDTO toDTOWithContent(NovelChapter chapter, String content);

    /**
     * DTO转换为实体
     *
     * @param chapterDTO 章节DTO
     * @return 章节实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    NovelChapter toEntity(NovelChapterDTO chapterDTO);

    /**
     * 实体列表转换为DTO列表
     *
     * @param chapters 章节实体列表
     * @return 章节DTO列表
     */
    List<NovelChapterDTO> toDTOList(List<NovelChapter> chapters);
}
