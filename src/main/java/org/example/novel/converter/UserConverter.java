package org.example.novel.converter;

import org.example.novel.dto.LoginResponseDTO;
import org.example.novel.dto.UserDTO;
import org.example.novel.entity.User;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 用户对象转换器
 */
@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface UserConverter {

    /**
     * 用户DTO转换为用户实体
     *
     * @param userDTO 用户DTO
     * @return 用户实体
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    @Mapping(target = "password", source = "password")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "role", source = "role")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    User toEntity(UserDTO userDTO);

    /**
     * 用户DTO转换为用户实体（用于注册，忽略ID）
     *
     * @param userDTO 用户DTO
     * @return 用户实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "username", source = "username")
    @Mapping(target = "password", source = "password")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "role", source = "role")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    User toEntityForRegister(UserDTO userDTO);

    /**
     * 用户实体转换为用户DTO
     *
     * @param user 用户实体
     * @return 用户DTO
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "email", source = "email")
    @Mapping(target = "role", source = "role")
    UserDTO toDTO(User user);

    /**
     * 用户实体转换为登录响应DTO
     *
     * @param user  用户实体
     * @param token JWT令牌
     * @return 登录响应DTO
     */
    @Mapping(target = "id", source = "user.id")
    @Mapping(target = "username", source = "user.username")
    @Mapping(target = "email", source = "user.email")
    @Mapping(target = "role", source = "user.role")
    @Mapping(target = "token", source = "token")
    LoginResponseDTO toLoginResponseDTO(User user, String token);
}
