package org.example.novel.converter;

import org.example.novel.dto.AudioDTO;
import org.example.novel.dto.CreateAudioRequestDTO;
import org.example.novel.dto.UpdateAudioRequestDTO;
import org.example.novel.entity.Audio;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * 音频素材转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper(componentModel = "spring")
public interface AudioConverter {

    /**
     * 实体转换为DTO
     *
     * @param audio 音频素材实体
     * @return 音频素材DTO
     */
    @Mapping(target = "audioUrl", ignore = true)
    AudioDTO toDTO(Audio audio);

    /**
     * 创建请求DTO转换为实体
     *
     * @param createRequest 创建请求DTO
     * @return 音频素材实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    Audio toEntity(CreateAudioRequestDTO createRequest);

    /**
     * 更新实体
     *
     * @param updateRequest 更新请求DTO
     * @param audio 目标实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    void updateEntity(UpdateAudioRequestDTO updateRequest, @MappingTarget Audio audio);

    /**
     * 实体列表转换为DTO列表
     *
     * @param audios 音频素材实体列表
     * @return 音频素材DTO列表
     */
    List<AudioDTO> toDTOList(List<Audio> audios);
}
