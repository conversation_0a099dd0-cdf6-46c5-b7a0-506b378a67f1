package org.example.novel.converter;

import org.example.novel.dto.NovelCharacterDTO;
import org.example.novel.entity.NovelCharacter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 小说角色转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Mapper(componentModel = "spring")
public interface NovelCharacterConverter {

    /**
     * 实体转换为DTO
     *
     * @param character 角色实体
     * @return 角色DTO
     */
    @Mapping(target = "roleInfo", ignore = true)
    @Mapping(target = "audioInfo", ignore = true)
    NovelCharacterDTO toDTO(NovelCharacter character);

    /**
     * DTO转换为实体
     *
     * @param characterDTO 角色DTO
     * @return 角色实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    NovelCharacter toEntity(NovelCharacterDTO characterDTO);

    /**
     * 实体列表转换为DTO列表
     *
     * @param characters 角色实体列表
     * @return 角色DTO列表
     */
    List<NovelCharacterDTO> toDTOList(List<NovelCharacter> characters);
}
