package org.example.novel.enums;

/**
 * 任务类型枚举
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public enum TaskType {
    
    /**
     * 角色解析
     */
    CHARACTER_PARSE("角色解析"),
    
    /**
     * 分镜解析
     */
    SCENE_PARSE("分镜解析"),
    
    /**
     * 分镜生成
     */
    STORYBOARD_GENERATE("分镜生成"),
    
    /**
     * 图片生成
     */
    IMAGE_GENERATE("图片生成"),
    
    /**
     * 音频生成
     */
    AUDIO_GENERATE("音频生成"),
    
    /**
     * 视频生成
     */
    VIDEO_GENERATE("视频生成"),
    
    /**
     * 文件处理
     */
    FILE_PROCESS("文件处理"),
    
    /**
     * 数据导入
     */
    DATA_IMPORT("数据导入"),
    
    /**
     * 数据导出
     */
    DATA_EXPORT("数据导出");

    private final String description;

    TaskType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
