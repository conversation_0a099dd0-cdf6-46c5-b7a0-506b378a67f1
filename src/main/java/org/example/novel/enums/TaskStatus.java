package org.example.novel.enums;

/**
 * 任务状态枚举
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
public enum TaskStatus {
    
    /**
     * 等待中
     */
    PENDING("等待中"),
    
    /**
     * 进行中
     */
    RUNNING("进行中"),
    
    /**
     * 已完成
     */
    COMPLETED("已完成"),
    
    /**
     * 失败
     */
    FAILED("失败"),
    
    /**
     * 已取消
     */
    CANCELLED("已取消");

    private final String description;

    TaskStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
