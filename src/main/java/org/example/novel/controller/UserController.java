package org.example.novel.controller;

import org.example.novel.common.Result;
import org.example.novel.dto.UserDTO;
import org.example.novel.entity.User;
import org.example.novel.converter.UserConverter;
import org.example.novel.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户信息管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserConverter userConverter;

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @ApiResponse(responseCode = "200", description = "操作成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = UserDTO.class)))
    public Result<UserDTO> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        return Result.success(userConverter.toDTO(user));
    }

    /**
     * 更新用户信息
     *
     * @param userDTO 用户信息
     * @return 更新结果
     */
    @PutMapping("/me")
    @Operation(summary = "更新当前用户信息", description = "更新当前登录用户的详细信息")
    @ApiResponse(responseCode = "200", description = "更新成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = UserDTO.class)))
    public Result<UserDTO> updateCurrentUser(@Valid @RequestBody UserDTO userDTO) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User currentUser = userService.findByUsername(username);
        
        // 设置ID，确保只能修改当前用户
        userDTO.setId(currentUser.getId());
        
        // 更新用户
        User user = userConverter.toEntity(userDTO);
        User updatedUser = userService.update(user);
        
        return Result.success("更新成功", userConverter.toDTO(updatedUser));
    }

    /**
     * 获取指定用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "获取指定用户信息", description = "根据用户ID获取用户详细信息，需要VIP权限")
    @ApiResponse(responseCode = "200", description = "操作成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = UserDTO.class)))
    public Result<UserDTO> getUserById(@PathVariable Long id) {
        User user = userService.findById(id);
        return Result.success(userConverter.toDTO(user));
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "删除用户", description = "根据用户ID删除用户，需要VIP权限")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<Void> deleteUser(@PathVariable Long id) {
        userService.deleteById(id);
        return Result.success();
    }
} 