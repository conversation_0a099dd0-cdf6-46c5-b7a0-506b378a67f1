package org.example.novel.controller;

import org.example.novel.common.Result;
import org.example.novel.dto.UserDTO;
import org.example.novel.entity.User;
import org.example.novel.converter.UserConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.time.LocalDateTime;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
@Tag(name = "测试接口", description = "用于测试系统功能的接口")
public class TestController {

    @Autowired
    private UserConverter userConverter;

    /**
     * 测试MapStruct转换器
     *
     * @return 测试结果
     */
    @GetMapping("/mapper")
    @Operation(summary = "测试MapStruct转换器", description = "测试UserConverter是否正常工作")
    public Result<UserDTO> testMapper() {
        // 创建一个测试用户实体
        User user = User.builder()
                .id(1L)
                .username("testuser")
                .password("password123")
                .email("<EMAIL>")
                .role("USER")
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        
        // 使用MapStruct转换器将实体转换为DTO
        UserDTO userDTO = userConverter.toDTO(user);
        
        return Result.success("MapStruct转换器测试成功", userDTO);
    }
    
    /**
     * 测试健康检查
     *
     * @return 测试结果
     */
    @GetMapping("/hello")
    @Operation(summary = "健康检查", description = "测试API是否正常工作")
    public Result<String> hello() {
        return Result.success("API正常工作");
    }
} 