package org.example.novel.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.common.Result;
import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.dto.ChapterSceneDTO;
import org.example.novel.dto.SceneParseRequestDTO;
import org.example.novel.dto.SceneParseResponseDTO;
import org.example.novel.service.AsyncTaskService;
import org.example.novel.service.ChapterSceneService;
import org.example.novel.util.SecurityUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 章节分镜管理控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/novels/{novelId}/scenes")
@RequiredArgsConstructor
@Tag(name = "章节分镜管理", description = "小说章节分镜的解析和管理功能")
@SecurityRequirement(name = "bearerAuth")
public class ChapterSceneController {

    private final ChapterSceneService chapterSceneService;
    private final AsyncTaskService asyncTaskService;

    /**
     * AI异步解析章节分镜
     */
    @PostMapping("/parse")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "AI异步解析章节分镜", description = "使用AI异步解析指定章节的分镜并创建分镜记录")
    @ApiResponse(responseCode = "200", description = "解析任务已启动", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = String.class)))
    public Result<Map<String, String>> parseScenesAsync(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Valid @RequestBody SceneParseRequestDTO parseRequest) {
        
        long startTime = System.currentTimeMillis();
        
        // 确保请求中的小说ID与路径参数一致
        parseRequest.setNovelId(novelId);
        
        Long userId = SecurityUtil.getCurrentUserId();
        String taskId = chapterSceneService.parseAndCreateScenesAsync(parseRequest, userId);
        
        long duration = System.currentTimeMillis() - startTime;
        log.info("异步分镜解析任务创建完成，taskId: {}, 耗时: {}ms, 章节数: {}", 
                taskId, duration, parseRequest.getChapterIds().size());
        
        Map<String, String> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("message", "分镜解析任务已启动，请使用taskId查询进度");
        result.put("duration", duration + "ms");
        
        return Result.success("解析任务已启动", result);
    }

    /**
     * 查询分镜解析任务状态
     */
    @GetMapping("/parse/status/{taskId}")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "查询分镜解析任务状态", description = "查询分镜解析任务的执行状态和结果")
    @ApiResponse(responseCode = "200", description = "查询成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = AsyncTaskResultDTO.class)))
    public Result<AsyncTaskResultDTO<SceneParseResponseDTO>> getParseTaskStatus(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        AsyncTaskResultDTO<SceneParseResponseDTO> taskResult = asyncTaskService.getTaskStatus(taskId, userId);
        
        return Result.success(taskResult);
    }

    /**
     * 获取章节分镜列表
     */
    @GetMapping("/chapters/{chapterId}")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "获取章节分镜列表", description = "获取指定章节的所有分镜")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<ChapterSceneDTO>> getChapterScenes(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        List<ChapterSceneDTO> scenes = chapterSceneService.getChapterScenes(chapterId, userId);
        
        return Result.success(scenes);
    }

    /**
     * 获取分镜详情
     */
    @GetMapping("/{sceneId}")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "获取分镜详情", description = "获取指定分镜的详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<ChapterSceneDTO> getSceneDetail(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "分镜ID", required = true)
            @PathVariable Long sceneId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        ChapterSceneDTO scene = chapterSceneService.getSceneDetail(sceneId, userId);
        
        return Result.success(scene);
    }

    /**
     * 更新分镜信息
     */
    @PutMapping("/{sceneId}")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "更新分镜信息", description = "更新指定分镜的信息")
    @ApiResponse(responseCode = "200", description = "更新成功")
    public Result<ChapterSceneDTO> updateScene(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "分镜ID", required = true)
            @PathVariable Long sceneId,
            @Valid @RequestBody ChapterSceneDTO sceneDTO) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        ChapterSceneDTO updatedScene = chapterSceneService.updateScene(sceneId, sceneDTO, userId);
        
        return Result.success("更新成功", updatedScene);
    }

    /**
     * 删除分镜
     */
    @DeleteMapping("/{sceneId}")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "删除分镜", description = "删除指定的分镜")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<Void> deleteScene(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "分镜ID", required = true)
            @PathVariable Long sceneId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        chapterSceneService.deleteScene(sceneId, userId);
        
        return Result.success("删除成功");
    }

    /**
     * 关联分镜到角色
     */
    @PostMapping("/{sceneId}/link-character")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "关联分镜到角色", description = "将分镜关联到指定角色")
    @ApiResponse(responseCode = "200", description = "关联成功")
    public Result<ChapterSceneDTO> linkCharacter(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "分镜ID", required = true)
            @PathVariable Long sceneId,
            @Parameter(description = "角色ID", required = true)
            @RequestParam Long characterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        ChapterSceneDTO updatedScene = chapterSceneService.linkCharacter(sceneId, characterId, userId);
        
        return Result.success("关联成功", updatedScene);
    }

    /**
     * 自动关联章节分镜中的说话者到角色
     */
    @PostMapping("/chapters/{chapterId}/auto-link")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "自动关联角色", description = "自动关联章节分镜中的说话者到角色")
    @ApiResponse(responseCode = "200", description = "关联完成")
    public Result<Map<String, Object>> autoLinkCharacters(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        Integer linkedCount = chapterSceneService.autoLinkCharacters(chapterId, userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("linkedCount", linkedCount);
        result.put("message", "自动关联完成，共关联 " + linkedCount + " 个分镜");
        
        return Result.success("关联完成", result);
    }
}
