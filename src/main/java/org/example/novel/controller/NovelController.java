package org.example.novel.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.common.Result;
import org.example.novel.dto.CreateNovelRequestDTO;
import org.example.novel.dto.NovelDTO;
import org.example.novel.dto.UpdateNovelRequestDTO;
import org.example.novel.dto.UploadNovelRequestDTO;
import org.example.novel.service.NovelService;
import org.example.novel.util.SecurityUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小说管理控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/novels")
@RequiredArgsConstructor
@Tag(name = "小说管理", description = "小说的创建、上传、查询、更新、删除等功能")
@SecurityRequirement(name = "bearerAuth")
public class NovelController {

    private final NovelService novelService;

    /**
     * 创建小说
     */
    @PostMapping
    @Operation(summary = "创建小说", description = "创建新的小说")
    @ApiResponse(responseCode = "200", description = "创建成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelDTO.class)))
    public Result<NovelDTO> createNovel(@Valid @RequestBody CreateNovelRequestDTO createRequest) {
        Long userId = SecurityUtil.getCurrentUserId();
        NovelDTO novelDTO = novelService.createNovel(createRequest, userId);
        return Result.success("创建成功", novelDTO);
    }

    /**
     * 上传小说文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传小说文件", description = "上传txt格式的小说文件，支持自动章节提取")
    @ApiResponse(responseCode = "200", description = "上传成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelDTO.class)))
    public Result<NovelDTO> uploadNovel(
            @Parameter(description = "小说文件（txt格式）", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "小说信息")
            @ModelAttribute @Valid UploadNovelRequestDTO uploadRequest) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelDTO novelDTO = novelService.uploadNovel(file, uploadRequest, userId);
        return Result.success("上传成功", novelDTO);
    }

    /**
     * 更新小说
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新小说", description = "更新指定ID的小说信息")
    @ApiResponse(responseCode = "200", description = "更新成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelDTO.class)))
    public Result<NovelDTO> updateNovel(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long id,
            @Valid @RequestBody UpdateNovelRequestDTO updateRequest) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelDTO novelDTO = novelService.updateNovel(id, updateRequest, userId);
        return Result.success("更新成功", novelDTO);
    }

    /**
     * 删除小说
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除小说", description = "删除指定ID的小说及其所有章节")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<Void> deleteNovel(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long id) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        boolean success = novelService.deleteNovel(id, userId);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }

    /**
     * 获取小说详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取小说详情", description = "根据ID获取小说的详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelDTO.class)))
    public Result<NovelDTO> getNovelById(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long id) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelDTO novelDTO = novelService.getNovelById(id, userId);
        return Result.success(novelDTO);
    }

    /**
     * 获取当前用户的小说列表
     */
    @GetMapping
    @Operation(summary = "获取小说列表", description = "分页获取当前用户的小说列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getUserNovels(
            @Parameter(description = "小说标题，支持模糊查询")
            @RequestParam(required = false) String title,
            @Parameter(description = "作者，支持模糊查询")
            @RequestParam(required = false) String author,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        List<NovelDTO> novels = novelService.getUserNovels(userId, title, author, page, size);
        Long total = novelService.countUserNovels(userId, title, author);
        
        Map<String, Object> result = new HashMap<>();
        result.put("novels", novels);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }

    /**
     * 获取所有小说列表（管理员权限）
     */
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "获取所有小说列表", description = "分页获取所有用户的小说列表，需要VIP权限")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getAllNovels(
            @Parameter(description = "小说标题，支持模糊查询")
            @RequestParam(required = false) String title,
            @Parameter(description = "作者，支持模糊查询")
            @RequestParam(required = false) String author,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        List<NovelDTO> novels = novelService.getAllNovels(title, author, page, size);
        Long total = novelService.countAllNovels(title, author);
        
        Map<String, Object> result = new HashMap<>();
        result.put("novels", novels);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }
}
