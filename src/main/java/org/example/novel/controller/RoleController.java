package org.example.novel.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.common.Result;
import org.example.novel.dto.CreateRoleRequestDTO;
import org.example.novel.dto.RoleDTO;
import org.example.novel.dto.UpdateRoleRequestDTO;
import org.example.novel.service.RoleService;
import org.example.novel.util.SecurityUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色素材管理控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/roles")
@RequiredArgsConstructor
@Tag(name = "角色素材管理", description = "角色素材的创建、查询、更新、删除等功能")
@SecurityRequirement(name = "bearerAuth")
public class RoleController {

    private final RoleService roleService;

    /**
     * 创建角色素材
     */
    @PostMapping
    @Operation(summary = "创建角色素材", description = "创建新的角色素材，用于AI绘画参考")
    @ApiResponse(responseCode = "200", description = "创建成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = RoleDTO.class)))
    public Result<RoleDTO> createRole(@Valid @RequestBody CreateRoleRequestDTO createRequest) {
        Long userId = SecurityUtil.getCurrentUserId();
        RoleDTO roleDTO = roleService.createRole(createRequest, userId);
        return Result.success("创建成功", roleDTO);
    }

    /**
     * 更新角色素材
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新角色素材", description = "更新指定ID的角色素材")
    @ApiResponse(responseCode = "200", description = "更新成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = RoleDTO.class)))
    public Result<RoleDTO> updateRole(
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long id,
            @Valid @RequestBody UpdateRoleRequestDTO updateRequest) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        RoleDTO roleDTO = roleService.updateRole(id, updateRequest, userId);
        return Result.success("更新成功", roleDTO);
    }

    /**
     * 删除角色素材
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色素材", description = "删除指定ID的角色素材")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<Void> deleteRole(
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long id) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        boolean success = roleService.deleteRole(id, userId);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }

    /**
     * 获取角色素材详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取角色素材详情", description = "根据ID获取角色素材的详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = RoleDTO.class)))
    public Result<RoleDTO> getRoleById(
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long id) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        RoleDTO roleDTO = roleService.getRoleById(id, userId);
        return Result.success(roleDTO);
    }

    /**
     * 获取当前用户的角色素材列表
     */
    @GetMapping
    @Operation(summary = "获取角色素材列表", description = "分页获取当前用户的角色素材列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getUserRoles(
            @Parameter(description = "角色名称，支持模糊查询")
            @RequestParam(required = false) String name,
            @Parameter(description = "标签，支持模糊查询")
            @RequestParam(required = false) String tags,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        List<RoleDTO> roles = roleService.getUserRoles(userId, name, tags, page, size);
        Long total = roleService.countUserRoles(userId, name, tags);
        
        Map<String, Object> result = new HashMap<>();
        result.put("roles", roles);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }

    /**
     * 获取所有角色素材列表（管理员权限）
     */
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "获取所有角色素材列表", description = "分页获取所有用户的角色素材列表，需要VIP权限")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getAllRoles(
            @Parameter(description = "角色名称，支持模糊查询")
            @RequestParam(required = false) String name,
            @Parameter(description = "标签，支持模糊查询")
            @RequestParam(required = false) String tags,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        List<RoleDTO> roles = roleService.getAllRoles(name, tags, page, size);
        Long total = roleService.countAllRoles(name, tags);
        
        Map<String, Object> result = new HashMap<>();
        result.put("roles", roles);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }
}
