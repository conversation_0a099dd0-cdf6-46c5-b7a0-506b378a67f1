package org.example.novel.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.common.Result;
import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.entity.AsyncTask;
import org.example.novel.enums.TaskStatus;
import org.example.novel.service.AsyncTaskService;
import org.example.novel.util.SecurityUtil;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 异步任务管理控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/tasks")
@RequiredArgsConstructor
@Tag(name = "异步任务管理", description = "异步任务的查询和管理功能")
@SecurityRequirement(name = "bearerAuth")
public class AsyncTaskController {

    private final AsyncTaskService asyncTaskService;

    /**
     * 获取任务状态
     */
    @GetMapping("/{taskId}")
    @Operation(summary = "获取任务状态", description = "根据任务ID获取任务的执行状态和结果")
    @ApiResponse(responseCode = "200", description = "获取成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = AsyncTaskResultDTO.class)))
    public Result<AsyncTaskResultDTO<?>> getTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        AsyncTaskResultDTO<?> taskResult = asyncTaskService.getTaskStatus(taskId, userId);
        return Result.success(taskResult);
    }

    /**
     * 获取用户的任务列表
     */
    @GetMapping
    @Operation(summary = "获取任务列表", description = "分页获取当前用户的任务列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getUserTasks(
            @Parameter(description = "任务类型")
            @RequestParam(required = false) String taskType,
            @Parameter(description = "任务状态")
            @RequestParam(required = false) TaskStatus status,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        List<AsyncTask> tasks = asyncTaskService.getUserTasks(userId, taskType, status, page, size);
        Long total = asyncTaskService.countUserTasks(userId, taskType, status);
        
        Map<String, Object> result = new HashMap<>();
        result.put("tasks", tasks);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取任务统计", description = "获取当前用户的任务统计信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getTaskStatistics() {
        Long userId = SecurityUtil.getCurrentUserId();
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 按状态统计
        statistics.put("pending", asyncTaskService.countUserTasks(userId, null, TaskStatus.PENDING));
        statistics.put("running", asyncTaskService.countUserTasks(userId, null, TaskStatus.RUNNING));
        statistics.put("completed", asyncTaskService.countUserTasks(userId, null, TaskStatus.COMPLETED));
        statistics.put("failed", asyncTaskService.countUserTasks(userId, null, TaskStatus.FAILED));
        
        // 按类型统计
        Map<String, Long> typeStatistics = new HashMap<>();
        typeStatistics.put("CHARACTER_PARSE", asyncTaskService.countUserTasks(userId, "CHARACTER_PARSE", null));
        typeStatistics.put("SCENE_PARSE", asyncTaskService.countUserTasks(userId, "SCENE_PARSE", null));
        typeStatistics.put("VIDEO_GENERATE", asyncTaskService.countUserTasks(userId, "VIDEO_GENERATE", null));
        statistics.put("byType", typeStatistics);
        
        // 总数
        statistics.put("total", asyncTaskService.countUserTasks(userId, null, null));
        
        return Result.success(statistics);
    }
}
