package org.example.novel.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.common.Result;
import org.example.novel.dto.BatchUploadChapterResponseDTO;
import org.example.novel.dto.NovelChapterDTO;
import org.example.novel.dto.UploadChapterRequestDTO;
import org.example.novel.service.NovelChapterService;
import org.example.novel.util.SecurityUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小说章节管理控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/novels/{novelId}/chapters")
@RequiredArgsConstructor
@Tag(name = "小说章节管理", description = "小说章节的创建、查询、更新、删除等功能")
@SecurityRequirement(name = "bearerAuth")
public class NovelChapterController {

    private final NovelChapterService novelChapterService;

    /**
     * 上传单个章节文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传章节文件", description = "上传单个txt格式的章节文件，智能解析文件名")
    @ApiResponse(responseCode = "200", description = "上传成功",
                content = @Content(mediaType = "application/json",
                schema = @Schema(implementation = NovelChapterDTO.class)))
    public Result<NovelChapterDTO> uploadChapterFile(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节文件（txt格式）", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "上传参数")
            @ModelAttribute @Valid UploadChapterRequestDTO uploadRequest) {

        Long userId = SecurityUtil.getCurrentUserId();
        NovelChapterDTO chapterDTO = novelChapterService.uploadChapterFile(novelId, file, uploadRequest, userId);
        return Result.success("上传成功", chapterDTO);
    }

    /**
     * 批量上传章节文件
     */
    @PostMapping("/batch-upload")
    @Operation(summary = "批量上传章节文件", description = "批量上传多个txt格式的章节文件")
    @ApiResponse(responseCode = "200", description = "批量上传完成",
                content = @Content(mediaType = "application/json",
                schema = @Schema(implementation = BatchUploadChapterResponseDTO.class)))
    public Result<BatchUploadChapterResponseDTO> batchUploadChapterFiles(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节文件列表（txt格式）", required = true)
            @RequestParam("files") MultipartFile[] files,
            @Parameter(description = "是否覆盖已存在的章节")
            @RequestParam(defaultValue = "false") Boolean overwrite) {

        Long userId = SecurityUtil.getCurrentUserId();
        BatchUploadChapterResponseDTO response = novelChapterService.batchUploadChapterFiles(novelId, files, overwrite, userId);
        return Result.success("批量上传完成", response);
    }

    /**
     * 手动创建章节（通过文本内容）
     */
    @PostMapping("/manual")
    @Operation(summary = "手动创建章节", description = "通过文本内容手动创建章节")
    @ApiResponse(responseCode = "200", description = "创建成功",
                content = @Content(mediaType = "application/json",
                schema = @Schema(implementation = NovelChapterDTO.class)))
    public Result<NovelChapterDTO> createChapterManually(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节标题", required = true)
            @RequestParam @NotBlank(message = "章节标题不能为空") String title,
            @Parameter(description = "章节内容", required = true)
            @RequestParam @NotBlank(message = "章节内容不能为空") String content) {

        Long userId = SecurityUtil.getCurrentUserId();
        NovelChapterDTO chapterDTO = novelChapterService.createChapter(novelId, title, content, userId);
        return Result.success("创建成功", chapterDTO);
    }

    /**
     * 更新章节（通过文本内容）
     */
    @PutMapping("/{chapterId}/manual")
    @Operation(summary = "手动更新章节", description = "通过文本内容更新指定ID的章节")
    @ApiResponse(responseCode = "200", description = "更新成功",
                content = @Content(mediaType = "application/json",
                schema = @Schema(implementation = NovelChapterDTO.class)))
    public Result<NovelChapterDTO> updateChapterManually(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId,
            @Parameter(description = "章节标题", required = true)
            @RequestParam @NotBlank(message = "章节标题不能为空") String title,
            @Parameter(description = "章节内容", required = true)
            @RequestParam @NotBlank(message = "章节内容不能为空") String content) {

        Long userId = SecurityUtil.getCurrentUserId();
        NovelChapterDTO chapterDTO = novelChapterService.updateChapter(chapterId, title, content, userId);
        return Result.success("更新成功", chapterDTO);
    }

    /**
     * 更新章节（通过文件上传）
     */
    @PutMapping("/{chapterId}/upload")
    @Operation(summary = "上传文件更新章节", description = "通过上传txt文件更新指定ID的章节")
    @ApiResponse(responseCode = "200", description = "更新成功",
                content = @Content(mediaType = "application/json",
                schema = @Schema(implementation = NovelChapterDTO.class)))
    public Result<NovelChapterDTO> updateChapterByFile(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId,
            @Parameter(description = "章节文件（txt格式）", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "章节标题（可选）")
            @RequestParam(required = false) String title) {

        Long userId = SecurityUtil.getCurrentUserId();

        // 读取文件内容
        String content;
        try {
            content = new String(file.getBytes(), java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            return Result.error("读取文件内容失败");
        }

        // 如果没有提供标题，尝试从文件名解析
        if (title == null || title.trim().isEmpty()) {
            org.example.novel.util.ChapterFileNameParser.ParseResult parseResult =
                org.example.novel.util.ChapterFileNameParser.parseFileName(file.getOriginalFilename());
            title = parseResult.getChapterTitle();
        }

        NovelChapterDTO chapterDTO = novelChapterService.updateChapter(chapterId, title, content, userId);
        return Result.success("更新成功", chapterDTO);
    }

    /**
     * 删除章节
     */
    @DeleteMapping("/{chapterId}")
    @Operation(summary = "删除章节", description = "删除指定ID的章节")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<Void> deleteChapter(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        boolean success = novelChapterService.deleteChapter(chapterId, userId);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }

    /**
     * 获取章节详情
     */
    @GetMapping("/{chapterId}")
    @Operation(summary = "获取章节详情", description = "根据ID获取章节的详细信息，包含内容")
    @ApiResponse(responseCode = "200", description = "获取成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelChapterDTO.class)))
    public Result<NovelChapterDTO> getChapterById(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelChapterDTO chapterDTO = novelChapterService.getChapterById(chapterId, userId);
        return Result.success(chapterDTO);
    }

    /**
     * 获取小说的章节列表
     */
    @GetMapping
    @Operation(summary = "获取章节列表", description = "分页获取指定小说的章节列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getNovelChapters(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        List<NovelChapterDTO> chapters = novelChapterService.getNovelChapters(novelId, userId, page, size);
        Long total = novelChapterService.countNovelChapters(novelId, userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("chapters", chapters);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }

    /**
     * 获取章节内容
     */
    @GetMapping("/{chapterId}/content")
    @Operation(summary = "获取章节内容", description = "获取指定章节的文本内容")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, String>> getChapterContent(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        String content = novelChapterService.getChapterContent(chapterId, userId);
        
        Map<String, String> result = new HashMap<>();
        result.put("content", content);
        
        return Result.success(result);
    }

    /**
     * 更新章节分镜解析状态
     */
    @PutMapping("/{chapterId}/scene-parsed")
    @Operation(summary = "更新分镜解析状态", description = "更新章节的分镜解析状态")
    @ApiResponse(responseCode = "200", description = "更新成功")
    public Result<Void> updateSceneParsedStatus(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId,
            @Parameter(description = "是否已分镜解析", required = true)
            @RequestParam Boolean isSceneParsed) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        boolean success = novelChapterService.updateSceneParsedStatus(chapterId, isSceneParsed, userId);
        if (success) {
            return Result.success("更新成功");
        } else {
            return Result.error("更新失败");
        }
    }

    /**
     * 更新章节角色解析状态
     */
    @PutMapping("/{chapterId}/character-parsed")
    @Operation(summary = "更新角色解析状态", description = "更新章节的角色解析状态")
    @ApiResponse(responseCode = "200", description = "更新成功")
    public Result<Void> updateCharacterParsedStatus(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable Long chapterId,
            @Parameter(description = "是否已角色解析", required = true)
            @RequestParam Boolean isCharacterParsed) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        boolean success = novelChapterService.updateCharacterParsedStatus(chapterId, isCharacterParsed, userId);
        if (success) {
            return Result.success("更新成功");
        } else {
            return Result.error("更新失败");
        }
    }
}
