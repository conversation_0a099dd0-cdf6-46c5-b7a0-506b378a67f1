package org.example.novel.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.common.Result;
import org.example.novel.dto.FileResourceDTO;
import org.example.novel.dto.MD5CheckRequestDTO;
import org.example.novel.dto.MD5CheckResponseDTO;
import org.example.novel.service.FileResourceService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件管理控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/files")
@RequiredArgsConstructor
@Tag(name = "文件管理", description = "文件上传、下载、预览等功能")
@SecurityRequirement(name = "bearerAuth")
public class FileController {

    private final FileResourceService fileResourceService;

    /**
     * 检查文件MD5是否已存在
     */
    @PostMapping("/check-md5")
    @Operation(summary = "检查文件MD5", description = "检查指定MD5的文件是否已存在，支持文件去重")
    @ApiResponse(responseCode = "200", description = "检查成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = MD5CheckResponseDTO.class)))
    public Result<MD5CheckResponseDTO> checkMD5(@Valid @RequestBody MD5CheckRequestDTO request) {
        MD5CheckResponseDTO response = fileResourceService.checkMD5(request);
        return Result.success(response);
    }

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "上传文件到MinIO，支持MD5去重，自动选择存储桶")
    @ApiResponse(responseCode = "200", description = "上传成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = FileResourceDTO.class)))
    public Result<FileResourceDTO> uploadFile(
            @Parameter(description = "要上传的文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        if (file == null || file.isEmpty()) {
            return Result.error("文件不能为空");
        }
        
        FileResourceDTO result = fileResourceService.uploadFile(file);
        return Result.success("上传成功", result);
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/{uuid}")
    @Operation(summary = "获取文件信息", description = "根据UUID获取文件的详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = FileResourceDTO.class)))
    public Result<FileResourceDTO> getFileInfo(
            @Parameter(description = "文件UUID", required = true)
            @PathVariable String uuid) {
        
        FileResourceDTO fileResource = fileResourceService.getByUuid(uuid);
        return Result.success(fileResource);
    }

    /**
     * 获取文件下载URL
     */
    @GetMapping("/{uuid}/download")
    @Operation(summary = "获取文件下载URL", description = "获取文件的下载链接")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, String>> getDownloadUrl(
            @Parameter(description = "文件UUID", required = true)
            @PathVariable String uuid) {
        
        String downloadUrl = fileResourceService.getDownloadUrl(uuid);
        Map<String, String> result = new HashMap<>();
        result.put("downloadUrl", downloadUrl);
        return Result.success(result);
    }

    /**
     * 获取文件预览URL
     */
    @GetMapping("/{uuid}/preview")
    @Operation(summary = "获取文件预览URL", description = "获取文件的预览链接，支持图片和音频")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, String>> getPreviewUrl(
            @Parameter(description = "文件UUID", required = true)
            @PathVariable String uuid) {
        
        String previewUrl = fileResourceService.getPreviewUrl(uuid);
        Map<String, String> result = new HashMap<>();
        result.put("previewUrl", previewUrl);
        return Result.success(result);
    }

    /**
     * 获取文件列表
     */
    @GetMapping
    @Operation(summary = "获取文件列表", description = "分页获取文件列表，支持按存储桶和文件类型筛选")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getFileList(
            @Parameter(description = "存储桶名称")
            @RequestParam(required = false) String bucket,
            @Parameter(description = "MIME类型前缀，如 image、audio")
            @RequestParam(required = false) String mimeType,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        List<FileResourceDTO> files = fileResourceService.getFileList(bucket, mimeType, page, size);
        Long total = fileResourceService.countFiles(bucket, mimeType);
        
        Map<String, Object> result = new HashMap<>();
        result.put("files", files);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{uuid}")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "删除文件", description = "删除指定UUID的文件，需要VIP权限")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<Void> deleteFile(
            @Parameter(description = "文件UUID", required = true)
            @PathVariable String uuid) {
        
        boolean success = fileResourceService.deleteFile(uuid);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }
}
