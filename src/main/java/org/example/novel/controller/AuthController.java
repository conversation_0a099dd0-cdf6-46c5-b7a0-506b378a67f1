package org.example.novel.controller;

import org.example.novel.common.Result;
import org.example.novel.dto.LoginRequestDTO;
import org.example.novel.dto.LoginResponseDTO;
import org.example.novel.dto.UserDTO;
import org.example.novel.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    @Autowired
    private AuthService authService;

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录接口，返回JWT令牌")
    @ApiResponse(responseCode = "200", description = "登录成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = LoginResponseDTO.class)))
    public Result<LoginResponseDTO> login(@Valid @RequestBody LoginRequestDTO loginRequest) {
        LoginResponseDTO loginResponse = authService.login(loginRequest);
        return Result.success("登录成功", loginResponse);
    }

    /**
     * 用户注册
     *
     * @param userDTO 用户信息
     * @return 注册结果
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "用户注册接口")
    @ApiResponse(responseCode = "200", description = "注册成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = UserDTO.class)))
    public Result<UserDTO> register(@Valid @RequestBody UserDTO userDTO) {
        UserDTO registeredUser = authService.register(userDTO);
        return Result.success("注册成功", registeredUser);
    }
} 