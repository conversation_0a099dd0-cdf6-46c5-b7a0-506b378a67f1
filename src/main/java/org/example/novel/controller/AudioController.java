package org.example.novel.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.common.Result;
import org.example.novel.dto.AudioDTO;
import org.example.novel.dto.CreateAudioRequestDTO;
import org.example.novel.dto.UpdateAudioRequestDTO;
import org.example.novel.service.AudioService;
import org.example.novel.util.SecurityUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 音频素材管理控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/audios")
@RequiredArgsConstructor
@Tag(name = "音频素材管理", description = "音频素材的创建、查询、更新、删除等功能")
@SecurityRequirement(name = "bearerAuth")
public class AudioController {

    private final AudioService audioService;

    /**
     * 创建音频素材
     */
    @PostMapping
    @Operation(summary = "创建音频素材", description = "创建新的音频素材，用于TTS参考")
    @ApiResponse(responseCode = "200", description = "创建成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = AudioDTO.class)))
    public Result<AudioDTO> createAudio(@Valid @RequestBody CreateAudioRequestDTO createRequest) {
        Long userId = SecurityUtil.getCurrentUserId();
        AudioDTO audioDTO = audioService.createAudio(createRequest, userId);
        return Result.success("创建成功", audioDTO);
    }

    /**
     * 更新音频素材
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新音频素材", description = "更新指定ID的音频素材")
    @ApiResponse(responseCode = "200", description = "更新成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = AudioDTO.class)))
    public Result<AudioDTO> updateAudio(
            @Parameter(description = "音频ID", required = true)
            @PathVariable Long id,
            @Valid @RequestBody UpdateAudioRequestDTO updateRequest) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        AudioDTO audioDTO = audioService.updateAudio(id, updateRequest, userId);
        return Result.success("更新成功", audioDTO);
    }

    /**
     * 删除音频素材
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除音频素材", description = "删除指定ID的音频素材")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<Void> deleteAudio(
            @Parameter(description = "音频ID", required = true)
            @PathVariable Long id) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        boolean success = audioService.deleteAudio(id, userId);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }

    /**
     * 获取音频素材详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取音频素材详情", description = "根据ID获取音频素材的详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = AudioDTO.class)))
    public Result<AudioDTO> getAudioById(
            @Parameter(description = "音频ID", required = true)
            @PathVariable Long id) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        AudioDTO audioDTO = audioService.getAudioById(id, userId);
        return Result.success(audioDTO);
    }

    /**
     * 获取当前用户的音频素材列表
     */
    @GetMapping
    @Operation(summary = "获取音频素材列表", description = "分页获取当前用户的音频素材列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getUserAudios(
            @Parameter(description = "音频名称，支持模糊查询")
            @RequestParam(required = false) String name,
            @Parameter(description = "音色类型")
            @RequestParam(required = false) String voiceType,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        List<AudioDTO> audios = audioService.getUserAudios(userId, name, voiceType, page, size);
        Long total = audioService.countUserAudios(userId, name, voiceType);
        
        Map<String, Object> result = new HashMap<>();
        result.put("audios", audios);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }

    /**
     * 获取所有音频素材列表（管理员权限）
     */
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('VIP')")
    @Operation(summary = "获取所有音频素材列表", description = "分页获取所有用户的音频素材列表，需要VIP权限")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getAllAudios(
            @Parameter(description = "音频名称，支持模糊查询")
            @RequestParam(required = false) String name,
            @Parameter(description = "音色类型")
            @RequestParam(required = false) String voiceType,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        List<AudioDTO> audios = audioService.getAllAudios(name, voiceType, page, size);
        Long total = audioService.countAllAudios(name, voiceType);
        
        Map<String, Object> result = new HashMap<>();
        result.put("audios", audios);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }
}
