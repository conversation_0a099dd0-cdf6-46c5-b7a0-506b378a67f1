package org.example.novel.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.common.Result;
import org.example.novel.dto.AsyncTaskResultDTO;
import org.example.novel.dto.CharacterParseRequestDTO;
import org.example.novel.dto.CharacterParseResponseDTO;
import org.example.novel.dto.NovelCharacterDTO;
import org.example.novel.service.AsyncTaskService;
import org.example.novel.service.NovelCharacterService;
import org.example.novel.util.SecurityUtil;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小说角色管理控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/novels/{novelId}/characters")
@RequiredArgsConstructor
@Tag(name = "小说角色管理", description = "小说角色的解析、创建、查询、更新、删除等功能")
@SecurityRequirement(name = "bearerAuth")
public class NovelCharacterController {

    private final NovelCharacterService novelCharacterService;
    private final AsyncTaskService asyncTaskService;

    /**
     * AI异步解析章节角色
     */
    @PostMapping("/parse")
    @Operation(summary = "AI异步解析章节角色", description = "使用AI异步解析指定章节中的角色并创建角色记录")
    @ApiResponse(responseCode = "200", description = "解析任务已启动",
                content = @Content(mediaType = "application/json",
                schema = @Schema(implementation = String.class)))
    public Result<Map<String, String>> parseCharactersAsync(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Valid @RequestBody CharacterParseRequestDTO parseRequest) {

        long startTime = System.currentTimeMillis();

        // 确保请求中的小说ID与路径参数一致
        parseRequest.setNovelId(novelId);

        Long userId = SecurityUtil.getCurrentUserId();
        String taskId = novelCharacterService.parseAndCreateCharactersAsync(parseRequest, userId);

        long duration = System.currentTimeMillis() - startTime;
        log.info("异步任务创建完成，taskId: {}, 耗时: {}ms, 章节数: {}",
                taskId, duration, parseRequest.getChapterIds().size());

        Map<String, String> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("message", "角色解析任务已启动，请使用taskId查询进度");
        result.put("duration", duration + "ms");

        return Result.success("解析任务已启动", result);
    }

    /**
     * 查询解析任务状态
     */
    @GetMapping("/parse/status/{taskId}")
    @Operation(summary = "查询解析任务状态", description = "查询角色解析任务的执行状态和结果")
    @ApiResponse(responseCode = "200", description = "查询成功",
                content = @Content(mediaType = "application/json",
                schema = @Schema(implementation = AsyncTaskResultDTO.class)))
    public Result<AsyncTaskResultDTO<CharacterParseResponseDTO>> getParseTaskStatus(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {

        Long userId = SecurityUtil.getCurrentUserId();
        AsyncTaskResultDTO<CharacterParseResponseDTO> taskResult = asyncTaskService.getTaskStatus(taskId, userId);

        return Result.success(taskResult);
    }

    /**
     * 手动创建角色
     */
    @PostMapping
    @Operation(summary = "创建角色", description = "手动创建小说角色")
    @ApiResponse(responseCode = "200", description = "创建成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelCharacterDTO.class)))
    public Result<NovelCharacterDTO> createCharacter(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "角色名称", required = true)
            @RequestParam @NotBlank(message = "角色名称不能为空") String name,
            @Parameter(description = "角色描述")
            @RequestParam(required = false) String description) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelCharacterDTO characterDTO = novelCharacterService.createCharacter(novelId, name, description, userId);
        return Result.success("创建成功", characterDTO);
    }

    /**
     * 更新角色
     */
    @PutMapping("/{characterId}")
    @Operation(summary = "更新角色", description = "更新指定ID的角色信息")
    @ApiResponse(responseCode = "200", description = "更新成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelCharacterDTO.class)))
    public Result<NovelCharacterDTO> updateCharacter(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long characterId,
            @Parameter(description = "角色名称")
            @RequestParam(required = false) String name,
            @Parameter(description = "角色描述")
            @RequestParam(required = false) String description) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelCharacterDTO characterDTO = novelCharacterService.updateCharacter(characterId, name, description, userId);
        return Result.success("更新成功", characterDTO);
    }

    /**
     * 关联角色素材
     */
    @PutMapping("/{characterId}/link-role")
    @Operation(summary = "关联角色素材", description = "为角色关联角色素材")
    @ApiResponse(responseCode = "200", description = "关联成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelCharacterDTO.class)))
    public Result<NovelCharacterDTO> linkRoleMaterial(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long characterId,
            @Parameter(description = "角色素材ID", required = true)
            @RequestParam @NotNull(message = "角色素材ID不能为空") Long roleId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelCharacterDTO characterDTO = novelCharacterService.linkRoleMaterial(characterId, roleId, userId);
        return Result.success("关联成功", characterDTO);
    }

    /**
     * 关联音频素材
     */
    @PutMapping("/{characterId}/link-audio")
    @Operation(summary = "关联音频素材", description = "为角色关联音频素材")
    @ApiResponse(responseCode = "200", description = "关联成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelCharacterDTO.class)))
    public Result<NovelCharacterDTO> linkAudioMaterial(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long characterId,
            @Parameter(description = "音频素材ID", required = true)
            @RequestParam @NotNull(message = "音频素材ID不能为空") Long audioId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelCharacterDTO characterDTO = novelCharacterService.linkAudioMaterial(characterId, audioId, userId);
        return Result.success("关联成功", characterDTO);
    }

    /**
     * 自动关联角色素材
     */
    @PutMapping("/{characterId}/auto-link")
    @Operation(summary = "自动关联角色素材", description = "根据角色名称自动关联角色素材")
    @ApiResponse(responseCode = "200", description = "关联完成")
    public Result<Void> autoLinkRoleMaterial(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long characterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        boolean success = novelCharacterService.autoLinkRoleMaterial(characterId, userId);
        if (success) {
            return Result.success("自动关联成功");
        } else {
            return Result.success("未找到匹配的角色素材");
        }
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{characterId}")
    @Operation(summary = "删除角色", description = "删除指定ID的角色")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<Void> deleteCharacter(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long characterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        boolean success = novelCharacterService.deleteCharacter(characterId, userId);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }

    /**
     * 获取角色详情
     */
    @GetMapping("/{characterId}")
    @Operation(summary = "获取角色详情", description = "根据ID获取角色的详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = NovelCharacterDTO.class)))
    public Result<NovelCharacterDTO> getCharacterById(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long characterId) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        NovelCharacterDTO characterDTO = novelCharacterService.getCharacterById(characterId, userId);
        return Result.success(characterDTO);
    }

    /**
     * 获取小说的角色列表
     */
    @GetMapping
    @Operation(summary = "获取角色列表", description = "分页获取指定小说的角色列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<Map<String, Object>> getNovelCharacters(
            @Parameter(description = "小说ID", required = true)
            @PathVariable Long novelId,
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") Integer size) {
        
        Long userId = SecurityUtil.getCurrentUserId();
        List<NovelCharacterDTO> characters = novelCharacterService.getNovelCharacters(novelId, userId, page, size);
        Long total = novelCharacterService.countNovelCharacters(novelId, userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("characters", characters);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }
}
