package org.example.novel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 小说章节实体类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NovelChapter {

    /**
     * 章节ID
     */
    private Long id;

    /**
     * 小说ID
     */
    private Long novelId;

    /**
     * 章节标题
     */
    private String title;

    /**
     * 章节序号
     */
    private Integer chapterIndex;

    /**
     * 章节内容MinIO的UUID
     */
    private String contentUuid;

    /**
     * 字数
     */
    private Integer wordCount;

    /**
     * 是否已进行分镜解析
     */
    private Boolean isSceneParsed;

    /**
     * 是否已解析角色
     */
    private Boolean isCharacterParsed;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
