package org.example.novel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 小说实体类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Novel {

    /**
     * 小说ID
     */
    private Long id;

    /**
     * 小说名称
     */
    private String title;

    /**
     * 作者
     */
    private String author;

    /**
     * 封面图片MinIO的UUID
     */
    private String coverUuid;

    /**
     * 小说简介
     */
    private String description;

    /**
     * 来源URL
     */
    private String sourceUrl;

    /**
     * 章节数量
     */
    private Integer chapterCount;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
