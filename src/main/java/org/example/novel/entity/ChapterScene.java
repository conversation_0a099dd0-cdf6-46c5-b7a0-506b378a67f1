package org.example.novel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 章节分镜实体类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChapterScene {

    /**
     * 分镜ID
     */
    private Long id;

    /**
     * 章节ID
     */
    private Long chapterId;

    /**
     * 分镜序号
     */
    private Integer sceneIndex;

    /**
     * 分镜文本内容
     */
    private String content;

    /**
     * 分镜AI绘画提示词
     */
    private String prompt;

    /**
     * 朗读者/说话者（null表示旁白）
     */
    private String speaker;

    /**
     * 关联的小说角色ID
     */
    private Long characterId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
