package org.example.novel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 分镜图片实体类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneImage {

    /**
     * 图片ID
     */
    private Long id;

    /**
     * 分镜ID
     */
    private Long sceneId;

    /**
     * 图片文件UUID（MinIO中的文件标识）
     */
    private String imageUuid;

    /**
     * 图片文件名
     */
    private String fileName;

    /**
     * 图片宽度
     */
    private Integer width;

    /**
     * 图片高度
     */
    private Integer height;

    /**
     * 图片大小（字节）
     */
    private Long fileSize;

    /**
     * 生成状态：PENDING(等待生成)、GENERATING(生成中)、COMPLETED(已完成)、FAILED(失败)
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
