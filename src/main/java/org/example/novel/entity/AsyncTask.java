package org.example.novel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.example.novel.enums.TaskStatus;

import java.time.LocalDateTime;

/**
 * 异步任务实体类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsyncTask {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 任务唯一标识
     */
    private String taskId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务名称/描述
     */
    private String taskName;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 进度百分比(0-100)
     */
    private Integer progress;

    /**
     * 当前执行步骤描述
     */
    private String currentStep;

    /**
     * 请求参数数据(JSON格式)
     */
    private String requestData;

    /**
     * 任务结果数据(JSON格式)
     */
    private String resultData;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时(毫秒)
     */
    private Long duration;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
