package org.example.novel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 音频素材实体类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Audio {

    /**
     * 音频ID
     */
    private Long id;

    /**
     * 音频名称
     */
    private String name;

    /**
     * 音频描述
     */
    private String description;

    /**
     * 音频文件MinIO的UUID
     */
    private String audioUuid;

    /**
     * 音频时长(秒)
     */
    private Integer duration;

    /**
     * 音色类型
     */
    private String voiceType;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
