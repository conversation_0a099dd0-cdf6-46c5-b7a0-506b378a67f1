package org.example.novel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文件资源实体类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileResource {

    /**
     * 资源ID
     */
    private Long id;

    /**
     * MinIO文件UUID
     */
    private String uuid;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 文件MD5值
     */
    private String md5;

    /**
     * 文件大小(字节)
     */
    private Long size;

    /**
     * 文件MIME类型
     */
    private String mimeType;

    /**
     * MinIO存储桶
     */
    private String bucket;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
