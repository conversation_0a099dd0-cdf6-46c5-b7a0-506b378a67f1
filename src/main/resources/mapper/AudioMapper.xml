<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.novel.mapper.AudioMapper">

    <resultMap id="BaseResultMap" type="org.example.novel.entity.Audio">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="audio_uuid" property="audioUuid" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="INTEGER"/>
        <result column="voice_type" property="voiceType" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, description, audio_uuid, duration, voice_type, user_id, create_time
    </sql>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM audio
        WHERE id = #{id}
    </select>

    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM audio
        WHERE user_id = #{userId}
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="voiceType != null and voiceType != ''">
            AND voice_type = #{voiceType}
        </if>
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM audio
        WHERE user_id = #{userId}
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="voiceType != null and voiceType != ''">
            AND voice_type = #{voiceType}
        </if>
    </select>

    <select id="findAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM audio
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="voiceType != null and voiceType != ''">
                AND voice_type = #{voiceType}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM audio
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="voiceType != null and voiceType != ''">
                AND voice_type = #{voiceType}
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="org.example.novel.entity.Audio" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO audio (name, description, audio_uuid, duration, voice_type, user_id)
        VALUES (#{name}, #{description}, #{audioUuid}, #{duration}, #{voiceType}, #{userId})
    </insert>

    <update id="update" parameterType="org.example.novel.entity.Audio">
        UPDATE audio
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="audioUuid != null">audio_uuid = #{audioUuid},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="voiceType != null">voice_type = #{voiceType},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM audio WHERE id = #{id}
    </delete>

    <select id="existsByIdAndUserId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM audio
        WHERE id = #{id} AND user_id = #{userId}
    </select>
</mapper>
