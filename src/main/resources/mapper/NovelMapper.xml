<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.novel.mapper.NovelMapper">

    <resultMap id="BaseResultMap" type="org.example.novel.entity.Novel">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="author" property="author" jdbcType="VARCHAR"/>
        <result column="cover_uuid" property="coverUuid" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="source_url" property="sourceUrl" jdbcType="VARCHAR"/>
        <result column="chapter_count" property="chapterCount" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, title, author, cover_uuid, description, source_url, chapter_count, user_id, create_time, update_time
    </sql>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel
        WHERE id = #{id}
    </select>

    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel
        WHERE user_id = #{userId}
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="author != null and author != ''">
            AND author LIKE CONCAT('%', #{author}, '%')
        </if>
        ORDER BY update_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM novel
        WHERE user_id = #{userId}
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="author != null and author != ''">
            AND author LIKE CONCAT('%', #{author}, '%')
        </if>
    </select>

    <select id="findAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="author != null and author != ''">
                AND author LIKE CONCAT('%', #{author}, '%')
            </if>
        </where>
        ORDER BY update_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM novel
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="author != null and author != ''">
                AND author LIKE CONCAT('%', #{author}, '%')
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="org.example.novel.entity.Novel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO novel (title, author, cover_uuid, description, source_url, chapter_count, user_id)
        VALUES (#{title}, #{author}, #{coverUuid}, #{description}, #{sourceUrl}, #{chapterCount}, #{userId})
    </insert>

    <update id="update" parameterType="org.example.novel.entity.Novel">
        UPDATE novel
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="author != null">author = #{author},</if>
            <if test="coverUuid != null">cover_uuid = #{coverUuid},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sourceUrl != null">source_url = #{sourceUrl},</if>
            <if test="chapterCount != null">chapter_count = #{chapterCount},</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateChapterCount">
        UPDATE novel
        SET chapter_count = #{chapterCount}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM novel WHERE id = #{id}
    </delete>

    <select id="existsByIdAndUserId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM novel
        WHERE id = #{id} AND user_id = #{userId}
    </select>
</mapper>
