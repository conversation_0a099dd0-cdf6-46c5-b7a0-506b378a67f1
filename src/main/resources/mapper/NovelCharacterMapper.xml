<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.novel.mapper.NovelCharacterMapper">

    <resultMap id="BaseResultMap" type="org.example.novel.entity.NovelCharacter">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="novel_id" property="novelId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="audio_id" property="audioId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, novel_id, name, description, role_id, audio_id, create_time, update_time
    </sql>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel_character
        WHERE id = #{id}
    </select>

    <select id="findByNovelIdWithPaging" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel_character
        WHERE novel_id = #{novelId}
        ORDER BY create_time ASC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findByNovelId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel_character
        WHERE novel_id = #{novelId}
        ORDER BY create_time ASC
    </select>

    <select id="findByNovelIdAndName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel_character
        WHERE novel_id = #{novelId} AND name = #{name}
    </select>

    <select id="countByNovelId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM novel_character
        WHERE novel_id = #{novelId}
    </select>

    <insert id="insert" parameterType="org.example.novel.entity.NovelCharacter" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO novel_character (novel_id, name, description, role_id, audio_id)
        VALUES (#{novelId}, #{name}, #{description}, #{roleId}, #{audioId})
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO novel_character (novel_id, name, description, role_id, audio_id)
        VALUES
        <foreach collection="characters" item="character" separator=",">
            (#{character.novelId}, #{character.name}, #{character.description}, #{character.roleId}, #{character.audioId})
        </foreach>
    </insert>

    <update id="update" parameterType="org.example.novel.entity.NovelCharacter">
        UPDATE novel_character
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="audioId != null">audio_id = #{audioId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM novel_character WHERE id = #{id}
    </delete>

    <delete id="deleteByNovelId">
        DELETE FROM novel_character WHERE novel_id = #{novelId}
    </delete>

    <select id="existsByIdAndNovelId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM novel_character
        WHERE id = #{id} AND novel_id = #{novelId}
    </select>

    <select id="existsByNovelIdAndName" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM novel_character
        WHERE novel_id = #{novelId} AND name = #{name}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>
</mapper>
