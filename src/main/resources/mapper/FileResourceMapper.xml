<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.novel.mapper.FileResourceMapper">

    <resultMap id="BaseResultMap" type="org.example.novel.entity.FileResource">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="original_name" property="originalName" jdbcType="VARCHAR"/>
        <result column="md5" property="md5" jdbcType="VARCHAR"/>
        <result column="size" property="size" jdbcType="BIGINT"/>
        <result column="mime_type" property="mimeType" jdbcType="VARCHAR"/>
        <result column="bucket" property="bucket" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, uuid, original_name, md5, size, mime_type, bucket, create_time
    </sql>

    <select id="findByMd5" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM file_resource
        WHERE md5 = #{md5}
    </select>

    <select id="findByUuid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM file_resource
        WHERE uuid = #{uuid}
    </select>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM file_resource
        WHERE id = #{id}
    </select>

    <select id="findList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM file_resource
        <where>
            <if test="bucket != null and bucket != ''">
                AND bucket = #{bucket}
            </if>
            <if test="mimeType != null and mimeType != ''">
                AND mime_type LIKE CONCAT(#{mimeType}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countList" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM file_resource
        <where>
            <if test="bucket != null and bucket != ''">
                AND bucket = #{bucket}
            </if>
            <if test="mimeType != null and mimeType != ''">
                AND mime_type LIKE CONCAT(#{mimeType}, '%')
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="org.example.novel.entity.FileResource" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO file_resource (uuid, original_name, md5, size, mime_type, bucket)
        VALUES (#{uuid}, #{originalName}, #{md5}, #{size}, #{mimeType}, #{bucket})
    </insert>

    <update id="update" parameterType="org.example.novel.entity.FileResource">
        UPDATE file_resource
        <set>
            <if test="uuid != null">uuid = #{uuid},</if>
            <if test="originalName != null">original_name = #{originalName},</if>
            <if test="md5 != null">md5 = #{md5},</if>
            <if test="size != null">size = #{size},</if>
            <if test="mimeType != null">mime_type = #{mimeType},</if>
            <if test="bucket != null">bucket = #{bucket},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM file_resource WHERE id = #{id}
    </delete>

    <delete id="deleteByUuid">
        DELETE FROM file_resource WHERE uuid = #{uuid}
    </delete>
</mapper>
