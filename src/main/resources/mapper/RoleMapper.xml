<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.novel.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="org.example.novel.entity.Role">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="prompt" property="prompt" jdbcType="LONGVARCHAR"/>
        <result column="image_uuid" property="imageUuid" jdbcType="VARCHAR"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, prompt, image_uuid, tags, user_id, create_time, update_time
    </sql>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM role
        WHERE id = #{id}
    </select>

    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM role
        WHERE user_id = #{userId}
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="tags != null and tags != ''">
            AND tags LIKE CONCAT('%', #{tags}, '%')
        </if>
        ORDER BY update_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM role
        WHERE user_id = #{userId}
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="tags != null and tags != ''">
            AND tags LIKE CONCAT('%', #{tags}, '%')
        </if>
    </select>

    <select id="findAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM role
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="tags != null and tags != ''">
                AND tags LIKE CONCAT('%', #{tags}, '%')
            </if>
        </where>
        ORDER BY update_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM role
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="tags != null and tags != ''">
                AND tags LIKE CONCAT('%', #{tags}, '%')
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="org.example.novel.entity.Role" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO role (name, prompt, image_uuid, tags, user_id)
        VALUES (#{name}, #{prompt}, #{imageUuid}, #{tags}, #{userId})
    </insert>

    <update id="update" parameterType="org.example.novel.entity.Role">
        UPDATE role
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="imageUuid != null">image_uuid = #{imageUuid},</if>
            <if test="tags != null">tags = #{tags},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM role WHERE id = #{id}
    </delete>

    <select id="existsByIdAndUserId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM role
        WHERE id = #{id} AND user_id = #{userId}
    </select>
</mapper>
