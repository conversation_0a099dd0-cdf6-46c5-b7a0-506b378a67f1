<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.novel.mapper.NovelChapterMapper">

    <resultMap id="BaseResultMap" type="org.example.novel.entity.NovelChapter">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="novel_id" property="novelId" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="chapter_index" property="chapterIndex" jdbcType="INTEGER"/>
        <result column="content_uuid" property="contentUuid" jdbcType="VARCHAR"/>
        <result column="word_count" property="wordCount" jdbcType="INTEGER"/>
        <result column="is_scene_parsed" property="isSceneParsed" jdbcType="BOOLEAN"/>
        <result column="is_character_parsed" property="isCharacterParsed" jdbcType="BOOLEAN"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, novel_id, title, chapter_index, content_uuid, word_count, is_scene_parsed, is_character_parsed, create_time
    </sql>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel_chapter
        WHERE id = #{id}
    </select>

    <select id="findByNovelId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel_chapter
        WHERE novel_id = #{novelId}
        ORDER BY chapter_index ASC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findByNovelIdAndChapterIndex" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM novel_chapter
        WHERE novel_id = #{novelId} AND chapter_index = #{chapterIndex}
    </select>

    <select id="countByNovelId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM novel_chapter
        WHERE novel_id = #{novelId}
    </select>

    <select id="getMaxChapterIndex" resultType="java.lang.Integer">
        SELECT MAX(chapter_index)
        FROM novel_chapter
        WHERE novel_id = #{novelId}
    </select>

    <insert id="insert" parameterType="org.example.novel.entity.NovelChapter" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO novel_chapter (novel_id, title, chapter_index, content_uuid, word_count, is_scene_parsed, is_character_parsed)
        VALUES (#{novelId}, #{title}, #{chapterIndex}, #{contentUuid}, #{wordCount}, #{isSceneParsed}, #{isCharacterParsed})
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO novel_chapter (novel_id, title, chapter_index, content_uuid, word_count, is_scene_parsed, is_character_parsed)
        VALUES
        <foreach collection="chapters" item="chapter" separator=",">
            (#{chapter.novelId}, #{chapter.title}, #{chapter.chapterIndex}, #{chapter.contentUuid}, #{chapter.wordCount}, #{chapter.isSceneParsed}, #{chapter.isCharacterParsed})
        </foreach>
    </insert>

    <update id="update" parameterType="org.example.novel.entity.NovelChapter">
        UPDATE novel_chapter
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="chapterIndex != null">chapter_index = #{chapterIndex},</if>
            <if test="contentUuid != null">content_uuid = #{contentUuid},</if>
            <if test="wordCount != null">word_count = #{wordCount},</if>
            <if test="isSceneParsed != null">is_scene_parsed = #{isSceneParsed},</if>
            <if test="isCharacterParsed != null">is_character_parsed = #{isCharacterParsed},</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateSceneParsedStatus">
        UPDATE novel_chapter
        SET is_scene_parsed = #{isSceneParsed}
        WHERE id = #{id}
    </update>

    <update id="updateCharacterParsedStatus">
        UPDATE novel_chapter
        SET is_character_parsed = #{isCharacterParsed}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM novel_chapter WHERE id = #{id}
    </delete>

    <delete id="deleteByNovelId">
        DELETE FROM novel_chapter WHERE novel_id = #{novelId}
    </delete>

    <select id="existsByIdAndNovelId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM novel_chapter
        WHERE id = #{id} AND novel_id = #{novelId}
    </select>
</mapper>
