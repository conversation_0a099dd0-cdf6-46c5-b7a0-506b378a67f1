<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.novel.mapper.AsyncTaskMapper">

    <resultMap id="BaseResultMap" type="org.example.novel.entity.AsyncTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="task_type" property="taskType" jdbcType="VARCHAR"/>
        <result column="task_name" property="taskName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="progress" property="progress" jdbcType="INTEGER"/>
        <result column="current_step" property="currentStep" jdbcType="VARCHAR"/>
        <result column="request_data" property="requestData" jdbcType="LONGVARCHAR"/>
        <result column="result_data" property="resultData" jdbcType="LONGVARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, user_id, task_type, task_name, status, progress, current_step,
        request_data, result_data, error_message, start_time, end_time, duration,
        create_time, update_time
    </sql>

    <select id="findByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM async_task
        WHERE task_id = #{taskId}
    </select>

    <select id="findByTaskIdAndUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM async_task
        WHERE task_id = #{taskId} AND user_id = #{userId}
    </select>

    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM async_task
        WHERE user_id = #{userId}
        <if test="taskType != null and taskType != ''">
            AND task_type = #{taskType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM async_task
        WHERE user_id = #{userId}
        <if test="taskType != null and taskType != ''">
            AND task_type = #{taskType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <insert id="insert" parameterType="org.example.novel.entity.AsyncTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO async_task (
            task_id, user_id, task_type, task_name, status, progress, current_step,
            request_data, result_data, error_message, start_time, end_time, duration
        ) VALUES (
            #{taskId}, #{userId}, #{taskType}, #{taskName}, #{status}, #{progress}, #{currentStep},
            #{requestData}, #{resultData}, #{errorMessage}, #{startTime}, #{endTime}, #{duration}
        )
    </insert>

    <update id="update" parameterType="org.example.novel.entity.AsyncTask">
        UPDATE async_task
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="resultData != null">result_data = #{resultData},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
        </set>
        WHERE task_id = #{taskId}
    </update>

    <update id="updateProgress">
        UPDATE async_task
        SET status = #{status},
            progress = #{progress},
            current_step = #{currentStep}
        WHERE task_id = #{taskId}
    </update>

    <update id="completeTask">
        UPDATE async_task
        SET status = #{status},
            progress = 100,
            result_data = #{resultData},
            error_message = #{errorMessage},
            end_time = #{endTime},
            duration = #{duration}
        WHERE task_id = #{taskId}
    </update>

    <delete id="deleteExpiredTasks">
        DELETE FROM async_task
        WHERE create_time &lt; #{expireTime}
    </delete>

    <select id="findRunningTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM async_task
        WHERE status = 'RUNNING'
        ORDER BY start_time ASC
    </select>
</mapper>
