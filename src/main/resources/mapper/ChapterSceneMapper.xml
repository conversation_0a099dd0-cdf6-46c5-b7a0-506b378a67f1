<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.novel.mapper.ChapterSceneMapper">

    <resultMap id="BaseResultMap" type="org.example.novel.entity.ChapterScene">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="chapter_id" property="chapterId" jdbcType="BIGINT"/>
        <result column="scene_index" property="sceneIndex" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="prompt" property="prompt" jdbcType="LONGVARCHAR"/>
        <result column="speaker" property="speaker" jdbcType="VARCHAR"/>
        <result column="character_id" property="characterId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, chapter_id, scene_index, content, prompt, speaker, character_id, create_time
    </sql>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chapter_scene
        WHERE id = #{id}
    </select>

    <select id="findByChapterId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chapter_scene
        WHERE chapter_id = #{chapterId}
        ORDER BY scene_index ASC
    </select>

    <select id="countByChapterId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM chapter_scene
        WHERE chapter_id = #{chapterId}
    </select>

    <select id="existsByChapterId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM chapter_scene
        WHERE chapter_id = #{chapterId}
        LIMIT 1
    </select>

    <insert id="insert" parameterType="org.example.novel.entity.ChapterScene" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chapter_scene (
            chapter_id, scene_index, content, prompt, speaker, character_id
        ) VALUES (
            #{chapterId}, #{sceneIndex}, #{content}, #{prompt}, #{speaker}, #{characterId}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO chapter_scene (
            chapter_id, scene_index, content, prompt, speaker, character_id
        ) VALUES
        <foreach collection="scenes" item="scene" separator=",">
            (#{scene.chapterId}, #{scene.sceneIndex}, #{scene.content}, #{scene.prompt}, #{scene.speaker}, #{scene.characterId})
        </foreach>
    </insert>

    <update id="update" parameterType="org.example.novel.entity.ChapterScene">
        UPDATE chapter_scene
        <set>
            <if test="content != null">content = #{content},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="speaker != null">speaker = #{speaker},</if>
            <if test="characterId != null">character_id = #{characterId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateCharacterLink">
        UPDATE chapter_scene
        SET character_id = #{characterId}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM chapter_scene
        WHERE id = #{id}
    </delete>

    <delete id="deleteByChapterId">
        DELETE FROM chapter_scene
        WHERE chapter_id = #{chapterId}
    </delete>
</mapper>
