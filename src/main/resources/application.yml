server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: novel-helper
  # 引入其他配置文件
  config:
    import:
      - classpath:application-ai.yml
      - classpath:application-prompts.yml
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************
    username: root
    password: ltdz1!5!
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: org.example.novel.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# MinIO配置
minio:
  endpoint: http://************:9000
  access-key: root
  secret-key: miniolya123
  bucket: novel

# JWT配置
jwt:
  secret: your-secret-key-here-should-be-very-long-and-secure-at-least-32-chars
  expiration: 864000000  # 24小时，单位：毫秒
  header: Authorization
  token-prefix: Bearer

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    disable-swagger-default-url: true
    url: /v3/api-docs
  default-produces-media-type: application/json
  default-consumes-media-type: application/json
  paths-to-match:
    - /auth/**
    - /users/**
    - /files/**
    - /roles/**
    - /audios/**
    - /novels/**
    - /test/**

# 日志配置
logging:
  level:
    root: info
    org.example.novel: debug
  file:
    name: logs/novel-helper.log

