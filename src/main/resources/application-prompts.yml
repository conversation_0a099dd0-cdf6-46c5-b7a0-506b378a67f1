# AI Prompt配置
prompts:
  # 角色解析提示词
  character-parse: |
    请分析以下小说章节内容，提取出所有出现的人物角色名称。
    
    要求：
    1. 只提取人物角色的名称，不要包含称谓、职业等描述
    2. 每个角色名称占一行
    3. 如果同一个角色有多个称呼方式，请选择最常用的正式名称
    4. 不要包含"我"、"你"、"他"等代词
    5. 不要包含群体称谓如"众人"、"大家"等
    6. 如果没有明确的人物角色，请返回"无"
    
    章节内容：
    {content}
    
    请按以下格式返回角色名称（每行一个）：
    角色1
    角色2
    角色3

  # 分镜解析提示词
  scene-parse: |
    请将以下小说章节内容划分为多个分镜片段，每个片段包含内容、说话者和AI绘画提示词。

    要求：
    1. 在不改变原小说内容的情况下，将文本划分为多个连续的片段
    2. 每个片段应该是一个完整的镜头或场景
    3. 识别每个片段的说话者（角色名称），如果是旁白则speaker为null
    4. 为每个片段生成适合AI绘画的提示词（英文，描述场景、人物、动作等）
    5. 返回JSON格式的数组

    章节内容：
    {content}

    请按以下JSON格式返回（确保返回有效的JSON数组）：
    [
      {
        "content": "第一个分镜的文本内容",
        "speaker": "角色名称或null",
        "prompt": "AI painting prompt in English describing the scene, characters, and actions"
      },
      {
        "content": "第二个分镜的文本内容",
        "speaker": null,
        "prompt": "Another AI painting prompt in English"
      }
    ]
  simplefy-prompt: |
    我希望你扮演专业小说内容优化编辑，按如下要求精炼优化小说：

    环境描写精简：识别并删减冗余、重复、对情节推进无益的环境描写，保留必要氛围感与空间感，让环境文字更凝练高效。
    人物对话优化：精简对话里的口语废话、冗长表述、重复信息，增强对话表现力与推动力，维持人物语气个性。
    核心情节与内容完整性：不偏离原情节，不删人物设定、重要事件、情感线索等核心内容，仅删 “填充” 性冗余表达。
    广告及无关信息清理：剔除广告、不明意义字母 / 数字 / 符号串，保证文本纯粹性。
    字数压缩：将原文（无论初始字数）压缩至约 1/3 篇幅（如 3000 字缩到 1000 字左右 ），精准提炼内容，保留故事精髓与关键逻辑，提升阅读流畅度和吸引力，100% 忠实原作精髓与完整性 。

  # 情感分析提示词
  emotion-analysis: |
    请分析以下文本的情感色彩和氛围。
    
    要求：
    1. 分析整体情感倾向（积极、消极、中性）
    2. 识别主要情感类型（喜悦、愤怒、悲伤、恐惧、惊讶等）
    3. 评估情感强度（1-10分）
    4. 简要说明情感产生的原因
    
    文本内容：
    {content}
    
    请按以下JSON格式返回：
    {
      "sentiment": "积极/消极/中性",
      "emotions": ["情感1", "情感2"],
      "intensity": 7,
      "reason": "情感产生的原因"
    }

  # 简单问答提示词（用于Ollama）
  simple-qa: |
    请简洁地回答以下问题：
    
    问题：{question}
    
    要求：
    1. 回答要准确、简洁
    2. 不要超过100字
    3. 如果不确定，请说明

  # 文本摘要提示词
  text-summary: |
    请为以下文本生成摘要。
    
    要求：
    1. 摘要长度不超过200字
    2. 保留主要信息和关键点
    3. 语言简洁流畅
    
    文本内容：
    {content}

  # 关键词提取提示词
  keyword-extraction: |
    请从以下文本中提取关键词。
    
    要求：
    1. 提取5-10个最重要的关键词
    2. 每个关键词占一行
    3. 按重要性排序
    
    文本内容：
    {content}
    
    关键词：
