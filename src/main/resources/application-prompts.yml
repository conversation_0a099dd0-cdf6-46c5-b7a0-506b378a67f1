# AI Prompt配置
prompts:
  # 角色解析提示词
  character-parse: |
    请分析以下小说章节内容，提取出所有出现的人物角色名称。
    
    要求：
    1. 只提取人物角色的名称，不要包含称谓、职业等描述
    2. 每个角色名称占一行
    3. 如果同一个角色有多个称呼方式，请选择最常用的正式名称
    4. 不要包含"我"、"你"、"他"等代词
    5. 不要包含群体称谓如"众人"、"大家"等
    6. 如果没有明确的人物角色，请返回"无"
    
    章节内容：
    {content}
    
    请按以下格式返回角色名称（每行一个）：
    角色1
    角色2
    角色3

  # 分镜解析提示词
  scene-parse: |
    核心任务
    你是一个专业的漫画分镜脚本家。你的任务是将输入的小说章节，拆分为一系列适合AI绘画的、结构化的漫画分镜片段。你需要严格遵循所有规则，并以指定的JSON格式输出。
    输出格式
    严格输出一个JSON列表。每个JSON对象代表一个分镜，包含以下四个键：
    ```json
    [
      {
        "content": "精简后的原文，主要保留对话或无法用画面直接展示的核心动作。",
        "speaker": "说话的角色名，或在无对话时使用 'Null'。",
        "characters": ["当前分镜中处于视觉焦点的、有名有姓的活体角色列表。"],
        "prompt": "用于AI绘画的英文提示词，详细描述画面构图、角色、动作、表情和环境。"
      }
    ]```
    核心规则与逻辑
    1. 分镜拆分原则：
    对话驱动: 每个角色的连续对话块应尽可能在一个分镜内。当说话者改变时，通常需要切换到新的分镜。
    动作驱动: 一个独立、重要的动作或事件（如“他拔出剑”、“门被踢开”）应构成一个分镜。
    视角/焦点切换: 当小说的叙述焦点从一个角色转移到另一个角色，或从整体环境转移到角色特写时，应创建新的分镜。
      2. content 内容处理：
    保留对话: 对话是核心，必须完整保留。
    精简旁白: 大力删减可以被prompt中画面描述所替代的旁白。
    可删除示例: “他愤怒地大喊”、“她微微一笑”、“二人对视”、“周围学生纷纷围观”。这些都应该通过prompt中的表情/动作/场景描述来体现，而不是留在content里。
    应保留示例: “一股寒意从他背脊升起”（内在感受）、“剑身上流淌着千年的光辉”（抽象描述）。
      3. characters 角色列表定义：
    仅包含具名角色: 只放入有名有姓的、活体的角色。最多3个。
    焦点原则: 只包含当前分镜画面视觉焦点中的角色。如果一个角色在场景中但当前分镜是另一个角色的特写，则可以不包含该背景角色。
    严禁包含: 绝对不要将人群、路人甲、或环境元素（如 "numerous viewers as shadow outlines"）放入此列表。
      4. prompt 英文提示词生成逻辑：
    角色指代 (最重要!): 使用位置描述词来指代characters列表中的角色，顺序严格对应。
    1个角色: characters: ["小明"] -> prompt中使用 the character 指代小明。
    2个角色: characters: ["小明", "教导主任"] -> prompt中用 the left character 指代小明, the right character 指代教导主任。
    3个角色: characters: ["李白", "杜甫", "小明"] -> prompt中用 the left character 指代李白, the middle character 指代杜甫, the right character 指代小明。
    包含所有元素: prompt必须清晰地描述出画面中的所有内容：
    角色: 使用上述规则指代。
    关键动作与表情: 例如 shouting anxiously, sneering coldly, drawing a sword。
    环境与背景: 必须继承并重复描述场景。如果上个分镜在“school gate”，这个分镜场景没变，依然要包含 school gate background。
    人群/氛围: 如果有围观人群，在此处用 ...with numerous viewers as blurred shadow outlines in the background 等方式描述。

    待处理的小说章节内容：
    {content}

    请严格按照上述规则处理并输出JSON格式的分镜列表：

  # 小说简化提示词
  simplify-prompt: |
    我希望你扮演专业小说内容优化编辑，按如下要求精炼优化小说：

    环境描写精简：识别并删减冗余、重复、对情节推进无益的环境描写，保留必要氛围感与空间感，让环境文字更凝练高效。
    人物对话优化：精简对话里的口语废话、冗长表述、重复信息，增强对话表现力与推动力，维持人物语气个性。
    核心情节与内容完整性：不偏离原情节，不删人物设定、重要事件、情感线索等核心内容，仅删 “填充” 性冗余表达。
    广告及无关信息清理：剔除广告、不明意义字母 / 数字 / 符号串，保证文本纯粹性。
    字数压缩：将原文（无论初始字数）压缩至约 1/3 篇幅（如 3000 字缩到 1000 字左右 ），精准提炼内容，保留故事精髓与关键逻辑，提升阅读流畅度和吸引力，100% 忠实原作精髓与完整性 。

    原始小说内容：
    {content}

    请直接返回优化后的小说内容，不要添加任何说明文字：

  # 情感分析提示词
  emotion-analysis: |
    请分析以下文本的情感色彩和氛围。
    
    要求：
    1. 分析整体情感倾向（积极、消极、中性）
    2. 识别主要情感类型（喜悦、愤怒、悲伤、恐惧、惊讶等）
    3. 评估情感强度（1-10分）
    4. 简要说明情感产生的原因
    
    文本内容：
    {content}
    
    请按以下JSON格式返回：
    {
      "sentiment": "积极/消极/中性",
      "emotions": ["情感1", "情感2"],
      "intensity": 7,
      "reason": "情感产生的原因"
    }

  # 简单问答提示词（用于Ollama）
  simple-qa: |
    请简洁地回答以下问题：
    
    问题：{question}
    
    要求：
    1. 回答要准确、简洁
    2. 不要超过100字
    3. 如果不确定，请说明

  # 文本摘要提示词
  text-summary: |
    请为以下文本生成摘要。
    
    要求：
    1. 摘要长度不超过200字
    2. 保留主要信息和关键点
    3. 语言简洁流畅
    
    文本内容：
    {content}

  # 关键词提取提示词
  keyword-extraction: |
    请从以下文本中提取关键词。
    
    要求：
    1. 提取5-10个最重要的关键词
    2. 每个关键词占一行
    3. 按重要性排序
    
    文本内容：
    {content}
    
    关键词：
