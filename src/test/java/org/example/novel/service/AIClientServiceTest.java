package org.example.novel.service;

import lombok.extern.slf4j.Slf4j;
import org.example.novel.config.AIProperties;
import org.example.novel.service.impl.AIClientServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.reactive.function.client.WebClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AI客户端服务测试类
 * 用于调试Gemini API调用问题
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "ai.gemini.retry.max-attempts=1",  // 设置重试次数为1
    "ai.gemini.proxy.enabled=true",
    "ai.gemini.proxy.host=127.0.0.1",
    "ai.gemini.proxy.port=7890",
    "ai.gemini.timeout=30s"
})
class AIClientServiceTest {

    @Autowired
    private AIClientService aiClientService;
    
    @Autowired
    private AIProperties aiProperties;
    
    @Autowired
    private WebClient geminiWebClient;

    @BeforeEach
    void setUp() {
        log.info("=== AI客户端测试开始 ===");
        log.info("代理配置 - 启用: {}, 地址: {}:{}", 
                aiProperties.getGemini().getProxy().getEnabled(),
                aiProperties.getGemini().getProxy().getHost(),
                aiProperties.getGemini().getProxy().getPort());
        log.info("重试配置 - 最大次数: {}", aiProperties.getGemini().getRetry().getMaxAttempts());
        log.info("API配置 - 基础URL: {}, 模型: {}", 
                aiProperties.getGemini().getBaseUrl(),
                aiProperties.getGemini().getModel());
    }

    @Test
    void testGeminiConnection() {
        log.info("=== 测试Gemini连接 ===");
        
        try {
            boolean connected = aiClientService.testGeminiConnection();
            log.info("Gemini连接测试结果: {}", connected ? "成功" : "失败");
            
            if (!connected) {
                log.warn("连接测试失败，但继续进行API调用测试");
            }
        } catch (Exception e) {
            log.error("连接测试异常: {}", e.getMessage(), e);
        }
    }

    @Test
    void testSimpleGeminiCall() {
        log.info("=== 测试简单Gemini API调用 ===");
        
        String testPrompt = "请回答：1+1等于几？只需要回答数字。";
        
        try {
            log.info("发送测试提示词: {}", testPrompt);
            String response = aiClientService.callGemini(testPrompt);
            
            log.info("API调用成功，响应: {}", response);
            assertNotNull(response, "响应不应为null");
            assertFalse(response.trim().isEmpty(), "响应不应为空");
            
        } catch (Exception e) {
            log.error("API调用失败: {}", e.getMessage(), e);
            
            // 打印详细的异常信息用于调试
            Throwable cause = e.getCause();
            while (cause != null) {
                log.error("异常原因: {}", cause.getMessage());
                cause = cause.getCause();
            }
            
            fail("Gemini API调用失败: " + e.getMessage());
        }
    }

    @Test
    void testProxyConfiguration() {
        log.info("=== 测试代理配置 ===");
        
        // 检查代理配置
        AIProperties.Proxy proxy = aiProperties.getGemini().getProxy();
        assertTrue(proxy.getEnabled(), "代理应该被启用");
        assertEquals("127.0.0.1", proxy.getHost(), "代理主机应为127.0.0.1");
        assertEquals(7890, proxy.getPort(), "代理端口应为7890");
        
        log.info("代理配置验证通过");
    }

    @Test
    void testRetryConfiguration() {
        log.info("=== 测试重试配置 ===");
        
        AIProperties.Retry retry = aiProperties.getGemini().getRetry();
        assertEquals(1, retry.getMaxAttempts(), "最大重试次数应为1");
        
        log.info("重试配置验证通过");
    }

    @Test
    void testDirectWebClientCall() {
        log.info("=== 测试直接WebClient调用 ===");
        
        try {
            // 构建简单的请求体
            String requestBody = """
                {
                  "contents": [{
                    "parts": [{
                      "text": "Hello, 请回答：今天是星期几？"
                    }]
                  }]
                }
                """;
            
            log.info("发送直接WebClient请求");
            String response = geminiWebClient.post()
                    .uri(aiProperties.getGemini().getBaseUrl() + "/models/{model}:generateContent?key={apiKey}",
                         aiProperties.getGemini().getModel(),
                         aiProperties.getGemini().getApiKey())
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(aiProperties.getGemini().getTimeout())
                    .block();
            
            log.info("直接WebClient调用成功，响应: {}", response);
            assertNotNull(response, "响应不应为null");
            
        } catch (Exception e) {
            log.error("直接WebClient调用失败: {}", e.getMessage(), e);
            
            // 检查是否是代理相关的错误
            String errorMsg = e.getMessage().toLowerCase();
            if (errorMsg.contains("connection") || errorMsg.contains("timeout") || errorMsg.contains("proxy")) {
                log.error("可能的代理连接问题，请检查:");
                log.error("1. 代理服务器是否正在运行 (127.0.0.1:7890)");
                log.error("2. 代理服务器是否允许HTTPS连接");
                log.error("3. 防火墙设置是否阻止了连接");
            }
            
            fail("直接WebClient调用失败: " + e.getMessage());
        }
    }

    @Test
    void testSystemProperties() {
        log.info("=== 检查系统代理属性 ===");

        // 检查系统代理属性
        String httpProxyHost = System.getProperty("http.proxyHost");
        String httpProxyPort = System.getProperty("http.proxyPort");
        String httpsProxyHost = System.getProperty("https.proxyHost");
        String httpsProxyPort = System.getProperty("https.proxyPort");

        log.info("系统HTTP代理: {}:{}", httpProxyHost, httpProxyPort);
        log.info("系统HTTPS代理: {}:{}", httpsProxyHost, httpsProxyPort);

        // 检查Java网络属性
        String useSystemProxies = System.getProperty("java.net.useSystemProxies");
        log.info("使用系统代理: {}", useSystemProxies);
    }

    @Test
    void testLongTextProcessing() {
        log.info("=== 测试长文本处理 ===");

        // 构建一个较长的测试文本，模拟实际的章节内容
        StringBuilder longText = new StringBuilder();
        longText.append("请简化以下小说内容：\n\n");

        // 添加重复的内容来模拟长章节
        for (int i = 0; i < 50; i++) {
            longText.append("第").append(i + 1).append("段：");
            longText.append("这是一个测试段落，包含了一些描述性的文字。");
            longText.append("主角走在路上，看到了美丽的风景，心情很好。");
            longText.append("他想起了过去的事情，感到有些怀念。");
            longText.append("突然，他听到了一个声音，转头看去。\n");
        }

        String testPrompt = longText.toString();
        log.info("测试提示词长度: {} 字符", testPrompt.length());

        try {
            long startTime = System.currentTimeMillis();
            String response = aiClientService.callGemini(testPrompt);
            long duration = System.currentTimeMillis() - startTime;

            log.info("长文本处理成功，耗时: {}ms", duration);
            log.info("响应长度: {} 字符", response.length());
            log.info("响应内容预览: {}", response.length() > 200 ?
                    response.substring(0, 200) + "..." : response);

            assertNotNull(response, "响应不应为null");
            assertFalse(response.trim().isEmpty(), "响应不应为空");

        } catch (Exception e) {
            log.error("长文本处理失败: {}", e.getMessage(), e);

            // 分析失败原因
            if (e.getMessage().contains("TimeoutException")) {
                log.error("超时错误 - 建议:");
                log.error("1. 增加timeout配置");
                log.error("2. 减少输入文本长度");
                log.error("3. 检查网络连接稳定性");
            }

            fail("长文本处理失败: " + e.getMessage());
        }
    }

    @Test
    void testMediumTextProcessing() {
        log.info("=== 测试中等长度文本处理 ===");

        // 构建中等长度的文本（约1000字符）
        String mediumText = """
                请简化以下小说内容：

                李明走在繁华的街道上，霓虹灯闪烁着五彩斑斓的光芒。他刚刚结束了一天的工作，
                疲惫但充实。街道两旁的商店里传来阵阵香味，让他想起了家乡的味道。

                "今天真是忙碌的一天。"他自言自语道，脚步不由得放慢了。

                突然，一个熟悉的身影出现在他的视野中。那是他的老朋友张华，正站在咖啡店门口。

                "李明！"张华挥手喊道，"好久不见！"

                李明惊喜地走了过去，两人热情地拥抱。他们决定进入咖啡店，好好聊聊近况。
                咖啡店里温暖而舒适，轻柔的音乐在空气中流淌。

                "你最近怎么样？"张华问道，眼中满含关切。

                "还不错，工作很忙，但是很充实。"李明回答，"你呢？"

                两人就这样聊了起来，时间在不知不觉中流逝。
                """;

        log.info("中等文本长度: {} 字符", mediumText.length());

        try {
            long startTime = System.currentTimeMillis();
            String response = aiClientService.callGemini(mediumText);
            long duration = System.currentTimeMillis() - startTime;

            log.info("中等文本处理成功，耗时: {}ms", duration);
            log.info("响应: {}", response);

            assertNotNull(response, "响应不应为null");
            assertFalse(response.trim().isEmpty(), "响应不应为空");

        } catch (Exception e) {
            log.error("中等文本处理失败: {}", e.getMessage(), e);
            fail("中等文本处理失败: " + e.getMessage());
        }
    }

    @Test
    void testFailureStrategy() {
        log.info("=== 测试失败策略 ===");

        // 测试一个可能导致失败的场景
        String problematicText = "测试无效的API密钥或网络问题时的失败处理";

        log.info("测试文本: {}", problematicText);

        try {
            String response = aiClientService.callGemini(problematicText);
            log.info("调用成功，响应: {}", response);

            // 如果成功了，说明网络和配置都正常
            assertNotNull(response);

        } catch (Exception e) {
            log.info("预期的失败情况，错误类型: {}", e.getClass().getSimpleName());
            log.info("错误信息: {}", e.getMessage());

            // 验证异常类型是否正确
            assertTrue(e.getMessage().contains("API调用") ||
                      e.getMessage().contains("连接") ||
                      e.getMessage().contains("超时"),
                      "应该是API相关的异常");
        }
    }
}
