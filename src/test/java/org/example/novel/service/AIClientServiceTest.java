package org.example.novel.service;

import lombok.extern.slf4j.Slf4j;
import org.example.novel.config.AIProperties;
import org.example.novel.service.impl.AIClientServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.reactive.function.client.WebClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AI客户端服务测试类
 * 用于调试Gemini API调用问题
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "ai.gemini.retry.max-attempts=1",  // 设置重试次数为1
    "ai.gemini.proxy.enabled=true",
    "ai.gemini.proxy.host=127.0.0.1",
    "ai.gemini.proxy.port=7890",
    "ai.gemini.timeout=30s"
})
class AIClientServiceTest {

    @Autowired
    private AIClientService aiClientService;
    
    @Autowired
    private AIProperties aiProperties;
    
    @Autowired
    private WebClient geminiWebClient;

    @BeforeEach
    void setUp() {
        log.info("=== AI客户端测试开始 ===");
        log.info("代理配置 - 启用: {}, 地址: {}:{}", 
                aiProperties.getGemini().getProxy().getEnabled(),
                aiProperties.getGemini().getProxy().getHost(),
                aiProperties.getGemini().getProxy().getPort());
        log.info("重试配置 - 最大次数: {}", aiProperties.getGemini().getRetry().getMaxAttempts());
        log.info("API配置 - 基础URL: {}, 模型: {}", 
                aiProperties.getGemini().getBaseUrl(),
                aiProperties.getGemini().getModel());
    }

    @Test
    void testGeminiConnection() {
        log.info("=== 测试Gemini连接 ===");
        
        try {
            boolean connected = aiClientService.testGeminiConnection();
            log.info("Gemini连接测试结果: {}", connected ? "成功" : "失败");
            
            if (!connected) {
                log.warn("连接测试失败，但继续进行API调用测试");
            }
        } catch (Exception e) {
            log.error("连接测试异常: {}", e.getMessage(), e);
        }
    }

    @Test
    void testSimpleGeminiCall() {
        log.info("=== 测试简单Gemini API调用 ===");
        
        String testPrompt = "请回答：1+1等于几？只需要回答数字。";
        
        try {
            log.info("发送测试提示词: {}", testPrompt);
            String response = aiClientService.callGemini(testPrompt);
            
            log.info("API调用成功，响应: {}", response);
            assertNotNull(response, "响应不应为null");
            assertFalse(response.trim().isEmpty(), "响应不应为空");
            
        } catch (Exception e) {
            log.error("API调用失败: {}", e.getMessage(), e);
            
            // 打印详细的异常信息用于调试
            Throwable cause = e.getCause();
            while (cause != null) {
                log.error("异常原因: {}", cause.getMessage());
                cause = cause.getCause();
            }
            
            fail("Gemini API调用失败: " + e.getMessage());
        }
    }

    @Test
    void testProxyConfiguration() {
        log.info("=== 测试代理配置 ===");
        
        // 检查代理配置
        AIProperties.Proxy proxy = aiProperties.getGemini().getProxy();
        assertTrue(proxy.getEnabled(), "代理应该被启用");
        assertEquals("127.0.0.1", proxy.getHost(), "代理主机应为127.0.0.1");
        assertEquals(7890, proxy.getPort(), "代理端口应为7890");
        
        log.info("代理配置验证通过");
    }

    @Test
    void testRetryConfiguration() {
        log.info("=== 测试重试配置 ===");
        
        AIProperties.Retry retry = aiProperties.getGemini().getRetry();
        assertEquals(1, retry.getMaxAttempts(), "最大重试次数应为1");
        
        log.info("重试配置验证通过");
    }

    @Test
    void testDirectWebClientCall() {
        log.info("=== 测试直接WebClient调用 ===");
        
        try {
            // 构建简单的请求体
            String requestBody = """
                {
                  "contents": [{
                    "parts": [{
                      "text": "Hello, 请回答：今天是星期几？"
                    }]
                  }]
                }
                """;
            
            log.info("发送直接WebClient请求");
            String response = geminiWebClient.post()
                    .uri(aiProperties.getGemini().getBaseUrl() + "/models/{model}:generateContent?key={apiKey}",
                         aiProperties.getGemini().getModel(),
                         aiProperties.getGemini().getApiKey())
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(aiProperties.getGemini().getTimeout())
                    .block();
            
            log.info("直接WebClient调用成功，响应: {}", response);
            assertNotNull(response, "响应不应为null");
            
        } catch (Exception e) {
            log.error("直接WebClient调用失败: {}", e.getMessage(), e);
            
            // 检查是否是代理相关的错误
            String errorMsg = e.getMessage().toLowerCase();
            if (errorMsg.contains("connection") || errorMsg.contains("timeout") || errorMsg.contains("proxy")) {
                log.error("可能的代理连接问题，请检查:");
                log.error("1. 代理服务器是否正在运行 (127.0.0.1:7890)");
                log.error("2. 代理服务器是否允许HTTPS连接");
                log.error("3. 防火墙设置是否阻止了连接");
            }
            
            fail("直接WebClient调用失败: " + e.getMessage());
        }
    }

    @Test
    void testSystemProperties() {
        log.info("=== 检查系统代理属性 ===");
        
        // 检查系统代理属性
        String httpProxyHost = System.getProperty("http.proxyHost");
        String httpProxyPort = System.getProperty("http.proxyPort");
        String httpsProxyHost = System.getProperty("https.proxyHost");
        String httpsProxyPort = System.getProperty("https.proxyPort");
        
        log.info("系统HTTP代理: {}:{}", httpProxyHost, httpProxyPort);
        log.info("系统HTTPS代理: {}:{}", httpsProxyHost, httpsProxyPort);
        
        // 检查Java网络属性
        String useSystemProxies = System.getProperty("java.net.useSystemProxies");
        log.info("使用系统代理: {}", useSystemProxies);
    }
}
