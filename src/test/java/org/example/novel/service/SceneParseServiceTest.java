package org.example.novel.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.service.impl.SceneParseServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Method;

/**
 * 分镜解析服务测试类
 */
@Slf4j
@SpringBootTest
class SceneParseServiceTest {

    @Autowired
    private SceneParseService sceneParseService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCleanAIResponse() throws Exception {
        log.info("=== 测试AI响应清理功能 ===");

        // 获取私有方法
        Method cleanMethod = SceneParseServiceImpl.class.getDeclaredMethod("cleanAIResponse", String.class);
        cleanMethod.setAccessible(true);

        // 测试markdown格式的响应
        String markdownResponse = """
                这是AI的分镜解析结果：
                
                ```json
                [
                  {
                    "content": "好！吃了这孙长老点名要入汤的百年灵芝，咱们就是自己人了。",
                    "speaker": "张大胖",
                    "characters": ["张大胖", "白小纯"],
                    "prompt": "A misty courtyard at night. The left character (Zhang Dapang) is smiling with great satisfaction."
                  },
                  {
                    "content": "白小纯这才明白这是同流合污。",
                    "speaker": "Narrator",
                    "characters": ["白小纯"],
                    "prompt": "The character looks thoughtful, a dawning realization on his face."
                  }
                ]
                ```
                
                以上就是分镜结果。
                """;

        String result = (String) cleanMethod.invoke(sceneParseService, markdownResponse);
        log.info("清理后的JSON: {}", result);

        // 验证结果是否为有效JSON
        try {
            objectMapper.readTree(result);
            log.info("✅ JSON格式验证通过");
        } catch (Exception e) {
            log.error("❌ JSON格式验证失败: {}", e.getMessage());
            throw e;
        }

        // 测试普通代码块格式
        String codeBlockResponse = """
                ```
                [
                  {
                    "content": "测试内容",
                    "speaker": "测试角色",
                    "characters": ["角色1", "角色2"],
                    "prompt": "Test prompt"
                  }
                ]
                ```
                """;

        String result2 = (String) cleanMethod.invoke(sceneParseService, codeBlockResponse);
        log.info("代码块清理后的JSON: {}", result2);

        // 测试无markdown格式的响应
        String plainResponse = """
                [
                  {
                    "content": "纯JSON格式",
                    "speaker": "角色",
                    "characters": ["角色1"],
                    "prompt": "Plain JSON prompt"
                  }
                ]
                """;

        String result3 = (String) cleanMethod.invoke(sceneParseService, plainResponse);
        log.info("纯JSON清理后的结果: {}", result3);
    }

    @Test
    void testCharacterListSerialization() {
        log.info("=== 测试角色列表序列化 ===");

        try {
            // 测试角色列表序列化
            java.util.List<String> characters = java.util.Arrays.asList("张大胖", "白小纯", "李青山");
            String json = objectMapper.writeValueAsString(characters);
            log.info("角色列表序列化结果: {}", json);

            // 测试反序列化
            com.fasterxml.jackson.core.type.TypeReference<java.util.List<String>> typeRef = 
                new com.fasterxml.jackson.core.type.TypeReference<java.util.List<String>>() {};
            java.util.List<String> deserializedCharacters = objectMapper.readValue(json, typeRef);
            log.info("反序列化结果: {}", deserializedCharacters);

            // 验证结果
            assert characters.equals(deserializedCharacters) : "序列化/反序列化结果不一致";
            log.info("✅ 角色列表序列化测试通过");

        } catch (Exception e) {
            log.error("❌ 角色列表序列化测试失败: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Test
    void testSceneDataStructure() {
        log.info("=== 测试分镜数据结构 ===");

        // 模拟AI返回的分镜数据
        String aiResponse = """
                [
                  {
                    "content": "好！吃了这孙长老点名要入汤的百年灵芝，咱们就是自己人了。",
                    "speaker": "张大胖",
                    "characters": ["张大胖", "白小纯"],
                    "prompt": "A misty courtyard at night. The left character (Zhang Dapang) is smiling with great satisfaction, sharing pieces of the spirit ganoderma with the other fat disciples (as blurred shadow outlines). The right character (Bai Xiaochun) looks thoughtful, a dawning realization on his face, as he understands he has become complicit and why Xu Baocai challenged him. Traditional Chinese fantasy style."
                  },
                  {
                    "content": "白小纯这才明白这是同流合污，而师兄们吃得胖胖的都没事，难怪许宝财下战书。",
                    "speaker": "Narrator",
                    "characters": ["白小纯"],
                    "prompt": "Close-up of the character with a dawning realization expression, understanding the situation. Traditional Chinese fantasy style with misty background."
                  }
                ]
                """;

        try {
            // 解析JSON
            com.fasterxml.jackson.core.type.TypeReference<java.util.List<java.util.Map<String, Object>>> typeRef = 
                new com.fasterxml.jackson.core.type.TypeReference<java.util.List<java.util.Map<String, Object>>>() {};
            java.util.List<java.util.Map<String, Object>> sceneData = objectMapper.readValue(aiResponse, typeRef);

            log.info("解析到 {} 个分镜", sceneData.size());

            for (int i = 0; i < sceneData.size(); i++) {
                java.util.Map<String, Object> scene = sceneData.get(i);
                String content = (String) scene.get("content");
                String speaker = (String) scene.get("speaker");
                @SuppressWarnings("unchecked")
                java.util.List<String> characters = (java.util.List<String>) scene.get("characters");
                String prompt = (String) scene.get("prompt");

                log.info("分镜 {}: 内容={}, 说话者={}, 角色={}, 提示词长度={}", 
                        i + 1, content.substring(0, Math.min(20, content.length())) + "...", 
                        speaker, characters, prompt.length());
            }

            log.info("✅ 分镜数据结构测试通过");

        } catch (Exception e) {
            log.error("❌ 分镜数据结构测试失败: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
