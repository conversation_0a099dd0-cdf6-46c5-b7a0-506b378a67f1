package org.example.novel.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.example.novel.dto.ChapterSceneDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * 章节分镜服务测试类
 */
@Slf4j
@SpringBootTest
class ChapterSceneServiceTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCharacterListSerialization() {
        log.info("=== 测试角色列表序列化功能 ===");

        try {
            // 测试角色列表
            List<String> characters = Arrays.asList("张大胖", "白小纯", "李青山");
            
            // 序列化
            String json = objectMapper.writeValueAsString(characters);
            log.info("序列化结果: {}", json);
            
            // 反序列化
            com.fasterxml.jackson.core.type.TypeReference<List<String>> typeRef = 
                new com.fasterxml.jackson.core.type.TypeReference<List<String>>() {};
            List<String> deserializedCharacters = objectMapper.readValue(json, typeRef);
            log.info("反序列化结果: {}", deserializedCharacters);
            
            // 验证
            assert characters.equals(deserializedCharacters) : "序列化/反序列化不一致";
            log.info("✅ 角色列表序列化测试通过");
            
        } catch (Exception e) {
            log.error("❌ 角色列表序列化测试失败: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Test
    void testChapterSceneDTOStructure() {
        log.info("=== 测试ChapterSceneDTO数据结构 ===");

        try {
            // 创建测试DTO
            ChapterSceneDTO sceneDTO = ChapterSceneDTO.builder()
                    .id(1L)
                    .chapterId(100L)
                    .sceneIndex(1)
                    .content("测试分镜内容")
                    .speaker("张大胖")
                    .characters(Arrays.asList("张大胖", "白小纯"))
                    .prompt("Test prompt for AI painting")
                    .build();

            log.info("创建的DTO: {}", sceneDTO);
            
            // 验证字段
            assert sceneDTO.getCharacters() != null : "角色列表不应为null";
            assert sceneDTO.getCharacters().size() == 2 : "角色列表大小应为2";
            assert sceneDTO.getCharacters().contains("张大胖") : "应包含张大胖";
            assert sceneDTO.getCharacters().contains("白小纯") : "应包含白小纯";
            
            log.info("✅ ChapterSceneDTO数据结构测试通过");
            
        } catch (Exception e) {
            log.error("❌ ChapterSceneDTO数据结构测试失败: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Test
    void testEmptyCharactersList() {
        log.info("=== 测试空角色列表处理 ===");

        try {
            // 测试空列表
            List<String> emptyList = Arrays.asList();
            String json = objectMapper.writeValueAsString(emptyList);
            log.info("空列表序列化结果: {}", json);
            
            // 测试null处理
            String nullJson = "null";
            try {
                com.fasterxml.jackson.core.type.TypeReference<List<String>> typeRef = 
                    new com.fasterxml.jackson.core.type.TypeReference<List<String>>() {};
                List<String> result = objectMapper.readValue(nullJson, typeRef);
                log.info("null反序列化结果: {}", result);
            } catch (Exception e) {
                log.info("null反序列化异常（预期）: {}", e.getMessage());
            }
            
            // 测试空字符串处理
            String emptyJson = "[]";
            com.fasterxml.jackson.core.type.TypeReference<List<String>> typeRef = 
                new com.fasterxml.jackson.core.type.TypeReference<List<String>>() {};
            List<String> emptyResult = objectMapper.readValue(emptyJson, typeRef);
            log.info("空数组反序列化结果: {}", emptyResult);
            
            assert emptyResult.isEmpty() : "空数组应该为空";
            log.info("✅ 空角色列表处理测试通过");
            
        } catch (Exception e) {
            log.error("❌ 空角色列表处理测试失败: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Test
    void testCharacterListManipulation() {
        log.info("=== 测试角色列表操作 ===");

        try {
            // 模拟现有角色列表
            String existingJson = "[\"张大胖\", \"李青山\"]";
            
            // 反序列化
            com.fasterxml.jackson.core.type.TypeReference<java.util.List<String>> typeRef = 
                new com.fasterxml.jackson.core.type.TypeReference<java.util.List<String>>() {};
            java.util.List<String> characters = objectMapper.readValue(existingJson, typeRef);
            log.info("现有角色列表: {}", characters);
            
            // 添加新角色
            String newCharacter = "白小纯";
            if (!characters.contains(newCharacter)) {
                characters.add(newCharacter);
                log.info("添加角色后: {}", characters);
            }
            
            // 序列化回JSON
            String updatedJson = objectMapper.writeValueAsString(characters);
            log.info("更新后的JSON: {}", updatedJson);
            
            // 验证
            assert characters.size() == 3 : "应该有3个角色";
            assert characters.contains("白小纯") : "应该包含新添加的角色";
            
            log.info("✅ 角色列表操作测试通过");
            
        } catch (Exception e) {
            log.error("❌ 角色列表操作测试失败: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
