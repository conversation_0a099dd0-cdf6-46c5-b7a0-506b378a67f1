package org.example.novel.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色批量处理测试
 * 验证去重和批量处理逻辑
 */
@Slf4j
@SpringBootTest
class CharacterBatchProcessTest {

    @Test
    void testCharacterDeduplication() {
        log.info("=== 测试角色去重逻辑 ===");

        // 模拟AI返回的分镜数据
        List<Map<String, Object>> sceneData = Arrays.asList(
            createScene("张大胖说话", "张大胖", Arrays.asList("张大胖", "白小纯")),
            createScene("白小纯回应", "白小纯", Arrays.asList("白小纯", "李青山")),
            createScene("旁白描述", "Narrator", Arrays.asList("张大胖", "白小纯", "李青山")),
            createScene("张大胖再次说话", "张大胖", Arrays.asList("张大胖")),
            createScene("李青山出现", "李青山", Arrays.asList("李青山", "白小纯"))
        );

        // 收集所有角色名称并去重
        Set<String> allCharacterNames = new HashSet<>();
        for (Map<String, Object> scene : sceneData) {
            @SuppressWarnings("unchecked")
            List<String> characterNames = (List<String>) scene.get("characters");
            if (characterNames != null) {
                allCharacterNames.addAll(characterNames);
            }
        }

        // 移除空值和旁白
        allCharacterNames.removeIf(name -> name == null || name.trim().isEmpty() || "Narrator".equals(name));

        log.info("原始分镜数量: {}", sceneData.size());
        log.info("去重后的角色列表: {}", allCharacterNames);
        log.info("去重后角色数量: {}", allCharacterNames.size());

        // 验证结果
        Set<String> expectedCharacters = Set.of("张大胖", "白小纯", "李青山");
        assert allCharacterNames.equals(expectedCharacters) : "角色去重结果不正确";

        log.info("✅ 角色去重测试通过");
    }

    @Test
    void testCharacterFrequencyAnalysis() {
        log.info("=== 测试角色出现频率分析 ===");

        // 模拟分镜数据
        List<Map<String, Object>> sceneData = Arrays.asList(
            createScene("场景1", "张大胖", Arrays.asList("张大胖", "白小纯")),
            createScene("场景2", "白小纯", Arrays.asList("白小纯")),
            createScene("场景3", "张大胖", Arrays.asList("张大胖", "白小纯", "李青山")),
            createScene("场景4", "李青山", Arrays.asList("李青山")),
            createScene("场景5", "Narrator", Arrays.asList("张大胖", "白小纯"))
        );

        // 统计角色出现频率
        Map<String, Integer> characterFrequency = new HashMap<>();
        for (Map<String, Object> scene : sceneData) {
            @SuppressWarnings("unchecked")
            List<String> characterNames = (List<String>) scene.get("characters");
            if (characterNames != null) {
                for (String name : characterNames) {
                    if (name != null && !name.trim().isEmpty() && !"Narrator".equals(name)) {
                        characterFrequency.merge(name, 1, Integer::sum);
                    }
                }
            }
        }

        log.info("角色出现频率统计:");
        characterFrequency.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> log.info("  {}: {} 次", entry.getKey(), entry.getValue()));

        // 验证主要角色
        assert characterFrequency.get("张大胖") == 3 : "张大胖应该出现3次";
        assert characterFrequency.get("白小纯") == 4 : "白小纯应该出现4次";
        assert characterFrequency.get("李青山") == 2 : "李青山应该出现2次";

        log.info("✅ 角色频率分析测试通过");
    }

    @Test
    void testBatchProcessingEfficiency() {
        log.info("=== 测试批量处理效率 ===");

        // 模拟大量分镜数据
        List<Map<String, Object>> sceneData = new ArrayList<>();
        String[] characters = {"张大胖", "白小纯", "李青山", "王小明", "赵小红"};
        
        // 创建100个分镜，每个分镜随机包含1-3个角色
        Random random = new Random();
        for (int i = 0; i < 100; i++) {
            List<String> sceneCharacters = new ArrayList<>();
            int characterCount = random.nextInt(3) + 1; // 1-3个角色
            
            for (int j = 0; j < characterCount; j++) {
                String character = characters[random.nextInt(characters.length)];
                if (!sceneCharacters.contains(character)) {
                    sceneCharacters.add(character);
                }
            }
            
            sceneData.add(createScene("场景" + (i + 1), sceneCharacters.get(0), sceneCharacters));
        }

        log.info("生成了 {} 个分镜", sceneData.size());

        // 统计原始查询次数（旧方法）
        int oldMethodQueries = 0;
        for (Map<String, Object> scene : sceneData) {
            @SuppressWarnings("unchecked")
            List<String> characterNames = (List<String>) scene.get("characters");
            if (characterNames != null) {
                for (String name : characterNames) {
                    if (name != null && !name.trim().isEmpty() && !"Narrator".equals(name)) {
                        oldMethodQueries++; // 每个角色一次查询
                    }
                }
            }
        }

        // 新方法：去重后的查询次数
        Set<String> uniqueCharacters = new HashSet<>();
        for (Map<String, Object> scene : sceneData) {
            @SuppressWarnings("unchecked")
            List<String> characterNames = (List<String>) scene.get("characters");
            if (characterNames != null) {
                uniqueCharacters.addAll(characterNames);
            }
        }
        uniqueCharacters.removeIf(name -> name == null || name.trim().isEmpty() || "Narrator".equals(name));
        
        int newMethodQueries = 1; // 一次批量查询

        log.info("旧方法查询次数: {}", oldMethodQueries);
        log.info("新方法查询次数: {}", newMethodQueries);
        log.info("查询次数减少: {}%", (1.0 - (double)newMethodQueries / oldMethodQueries) * 100);
        log.info("去重后的唯一角色数: {}", uniqueCharacters.size());

        assert newMethodQueries < oldMethodQueries : "新方法应该减少查询次数";

        log.info("✅ 批量处理效率测试通过");
    }

    @Test
    void testEmptyAndNullHandling() {
        log.info("=== 测试空值和null处理 ===");

        // 包含各种边界情况的测试数据
        List<Map<String, Object>> sceneData = Arrays.asList(
            createScene("正常场景", "张大胖", Arrays.asList("张大胖", "白小纯")),
            createScene("空角色列表", "Narrator", Arrays.asList()),
            createScene("null角色列表", "Narrator", null),
            createScene("包含空字符串", "张大胖", Arrays.asList("张大胖", "", "白小纯")),
            createScene("包含null", "白小纯", Arrays.asList("白小纯", null, "李青山")),
            createScene("只有Narrator", "Narrator", Arrays.asList("Narrator"))
        );

        // 处理逻辑
        Set<String> allCharacterNames = new HashSet<>();
        for (Map<String, Object> scene : sceneData) {
            @SuppressWarnings("unchecked")
            List<String> characterNames = (List<String>) scene.get("characters");
            if (characterNames != null) {
                allCharacterNames.addAll(characterNames);
            }
        }

        // 移除空值和旁白
        allCharacterNames.removeIf(name -> name == null || name.trim().isEmpty() || "Narrator".equals(name));

        log.info("处理后的角色列表: {}", allCharacterNames);

        // 验证结果
        Set<String> expectedCharacters = Set.of("张大胖", "白小纯", "李青山");
        assert allCharacterNames.equals(expectedCharacters) : "空值处理结果不正确";

        log.info("✅ 空值和null处理测试通过");
    }

    private Map<String, Object> createScene(String content, String speaker, List<String> characters) {
        Map<String, Object> scene = new HashMap<>();
        scene.put("content", content);
        scene.put("speaker", speaker);
        scene.put("characters", characters);
        scene.put("prompt", "Test prompt for " + content);
        return scene;
    }
}
