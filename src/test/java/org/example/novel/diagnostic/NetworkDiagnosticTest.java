package org.example.novel.diagnostic;

import lombok.extern.slf4j.Slf4j;
import org.example.novel.service.AIClientService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.net.*;
import java.time.Duration;
import java.time.Instant;

/**
 * 网络诊断测试工具
 * 用于排查Gemini API连接问题
 */
@Slf4j
@SpringBootTest
class NetworkDiagnosticTest {

    @Autowired
    private AIClientService aiClientService;

    @Test
    void fullNetworkDiagnostic() {
        log.info("=== 🔍 开始网络诊断 ===");
        
        // 1. 检查代理服务器
        testProxyServer();
        
        // 2. 检查目标服务器连通性
        testTargetServer();
        
        // 3. 检查DNS解析
        testDNSResolution();
        
        // 4. 测试HTTP连接
        testHTTPConnection();
        
        // 5. 测试Gemini API
        testGeminiAPI();
        
        log.info("=== 🏁 网络诊断完成 ===");
    }

    private void testProxyServer() {
        log.info("--- 📡 检查代理服务器 ---");
        
        String proxyHost = "127.0.0.1";
        int proxyPort = 7890;
        
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(proxyHost, proxyPort), 5000);
            log.info("✅ 代理服务器连接成功: {}:{}", proxyHost, proxyPort);
        } catch (IOException e) {
            log.error("❌ 代理服务器连接失败: {}:{}", proxyHost, proxyPort);
            log.error("   错误: {}", e.getMessage());
            log.error("   🔧 请检查代理软件是否正在运行");
        }
    }

    private void testTargetServer() {
        log.info("--- 🌐 检查目标服务器连通性 ---");
        
        String targetHost = "gemini.liuyuao.xyz";
        int targetPort = 443; // HTTPS端口
        
        try (Socket socket = new Socket()) {
            Instant start = Instant.now();
            socket.connect(new InetSocketAddress(targetHost, targetPort), 10000);
            Duration duration = Duration.between(start, Instant.now());
            
            log.info("✅ 目标服务器连接成功: {}:{}", targetHost, targetPort);
            log.info("   连接耗时: {}ms", duration.toMillis());
        } catch (IOException e) {
            log.error("❌ 目标服务器连接失败: {}:{}", targetHost, targetPort);
            log.error("   错误: {}", e.getMessage());
            log.error("   🔧 请检查网络连接或服务器状态");
        }
    }

    private void testDNSResolution() {
        log.info("--- 🔍 检查DNS解析 ---");
        
        String hostname = "gemini.liuyuao.xyz";
        
        try {
            InetAddress[] addresses = InetAddress.getAllByName(hostname);
            log.info("✅ DNS解析成功: {}", hostname);
            for (InetAddress addr : addresses) {
                log.info("   IP地址: {}", addr.getHostAddress());
            }
        } catch (UnknownHostException e) {
            log.error("❌ DNS解析失败: {}", hostname);
            log.error("   错误: {}", e.getMessage());
            log.error("   🔧 请检查DNS设置或网络连接");
        }
    }

    private void testHTTPConnection() {
        log.info("--- 🌍 检查HTTP连接 ---");
        
        try {
            // 设置代理
            System.setProperty("http.proxyHost", "127.0.0.1");
            System.setProperty("http.proxyPort", "7890");
            System.setProperty("https.proxyHost", "127.0.0.1");
            System.setProperty("https.proxyPort", "7890");
            
            URL url = new URL("https://gemini.liuyuao.xyz");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.setRequestMethod("GET");
            
            int responseCode = connection.getResponseCode();
            log.info("✅ HTTP连接成功，响应码: {}", responseCode);
            
        } catch (Exception e) {
            log.error("❌ HTTP连接失败");
            log.error("   错误: {}", e.getMessage());
            
            if (e instanceof ConnectException) {
                log.error("   🔧 连接被拒绝，请检查代理设置");
            } else if (e instanceof SocketTimeoutException) {
                log.error("   🔧 连接超时，请检查网络连接");
            }
        } finally {
            // 清理系统属性
            System.clearProperty("http.proxyHost");
            System.clearProperty("http.proxyPort");
            System.clearProperty("https.proxyHost");
            System.clearProperty("https.proxyPort");
        }
    }

    private void testGeminiAPI() {
        log.info("--- 🤖 测试Gemini API ---");
        
        try {
            boolean connected = aiClientService.testGeminiConnection();
            if (connected) {
                log.info("✅ Gemini API连接测试成功");
            } else {
                log.error("❌ Gemini API连接测试失败");
            }
        } catch (Exception e) {
            log.error("❌ Gemini API测试异常: {}", e.getMessage(), e);
        }
    }

    @Test
    void quickProxyTest() {
        log.info("=== 🚀 快速代理测试 ===");
        
        String proxyHost = "127.0.0.1";
        int proxyPort = 7890;
        
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(proxyHost, proxyPort), 3000);
            log.info("✅ 代理服务器运行正常");
            
            // 测试简单的API调用
            try {
                String response = aiClientService.callGemini("测试");
                log.info("✅ API调用成功，响应长度: {}", response.length());
            } catch (Exception e) {
                log.error("❌ API调用失败: {}", e.getMessage());
            }
            
        } catch (IOException e) {
            log.error("❌ 代理服务器无法连接");
            log.error("🔧 解决方案：");
            log.error("   1. 启动代理软件（如Clash、V2Ray等）");
            log.error("   2. 确保代理监听端口为7890");
            log.error("   3. 检查代理软件配置");
        }
    }

    @Test
    void systemPropertiesCheck() {
        log.info("=== ⚙️ 系统属性检查 ===");
        
        log.info("Java版本: {}", System.getProperty("java.version"));
        log.info("操作系统: {}", System.getProperty("os.name"));
        log.info("网络代理设置:");
        log.info("  http.proxyHost: {}", System.getProperty("http.proxyHost"));
        log.info("  http.proxyPort: {}", System.getProperty("http.proxyPort"));
        log.info("  https.proxyHost: {}", System.getProperty("https.proxyHost"));
        log.info("  https.proxyPort: {}", System.getProperty("https.proxyPort"));
        log.info("  java.net.useSystemProxies: {}", System.getProperty("java.net.useSystemProxies"));
    }
}
