-- 数据库迁移脚本：修改chapter_scene表结构
-- 将character_id字段改为characters字段（JSON格式存储角色名称列表）

-- 1. 添加新的characters字段
ALTER TABLE chapter_scene ADD COLUMN characters LONGTEXT COMMENT '分镜中的角色名称列表（JSON格式）';

-- 2. 将现有的character_id数据迁移到characters字段
-- 注意：这个脚本假设你有novel_character表来查找角色名称
UPDATE chapter_scene cs 
LEFT JOIN novel_character nc ON cs.character_id = nc.id 
SET cs.characters = CASE 
    WHEN nc.name IS NOT NULL THEN CONCAT('["', nc.name, '"]')
    ELSE '[]'
END
WHERE cs.character_id IS NOT NULL;

-- 3. 对于character_id为NULL的记录，设置为空数组
UPDATE chapter_scene 
SET characters = '[]' 
WHERE character_id IS NULL AND characters IS NULL;

-- 4. 删除旧的character_id字段（可选，建议先备份数据）
-- ALTER TABLE chapter_scene DROP COLUMN character_id;

-- 验证迁移结果
SELECT 
    id,
    chapter_id,
    scene_index,
    speaker,
    character_id,
    characters,
    create_time
FROM chapter_scene 
LIMIT 10;

-- 检查数据完整性
SELECT 
    COUNT(*) as total_scenes,
    COUNT(CASE WHEN characters IS NOT NULL THEN 1 END) as scenes_with_characters,
    COUNT(CASE WHEN characters = '[]' THEN 1 END) as scenes_with_empty_characters,
    COUNT(CASE WHEN characters IS NULL THEN 1 END) as scenes_with_null_characters
FROM chapter_scene;
