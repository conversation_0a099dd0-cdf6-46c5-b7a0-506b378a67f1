# AI视频生成项目接口文档
http://localhost:8080/api/swagger-ui.html
## 错误响应

### 认证错误

```json
{
  "code": 401,
  "message": "未授权：用户名或密码错误",
  "data": null
}
```

### 权限错误

```json
{
  "code": 403,
  "message": "无权限访问",
  "data": null
}
```

### 参数错误

```json
{
  "code": 400,
  "message": "用户名不能为空, 密码长度必须在6-20之间",
  "data": null
}
```

### 业务错误

```json
{
  "code": 400,
  "message": "用户名已存在",
  "data": null
}
```

### 服务器错误

```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```
## 1. 用户认证与授权接口

### 1.1 用户注册

- **接口URL**: `/api/auth/register`
- **请求方式**: POST
- **接口描述**: 用户注册接口，创建新用户账号
- **请求参数**:

```json
{
  "username": "testuser",
  "password": "password123",
  "email": "<EMAIL>",
  "role": "USER"  // 可选，默认为"USER"
}
```

- **参数说明**:
  - `username`: 用户名，必填，长度4-20，只能包含字母、数字和下划线
  - `password`: 密码，必填，长度6-20
  - `email`: 邮箱，可选，但必须符合邮箱格式
  - `role`: 角色，可选，默认为"USER"，可选值："USER"(普通用户)、"VIP"(VIP用户)

- **响应示例**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 1.2 用户登录

- **接口URL**: `/api/auth/login`
- **请求方式**: POST
- **接口描述**: 用户登录接口，返回JWT令牌
- **请求参数**:

```json
{
  "username": "testuser",
  "password": "password123"
}
```

- **参数说明**:
  - `username`: 用户名，必填
  - `password`: 密码，必填

- **响应示例**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER",
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImlhdCI6MTYxNjEyMzQ1NiwiZXhwIjoxNjE2MjA5ODU2fQ.hV6pGvAVV-Qg0XLzTJgGPz8I_lHvGJIJ9ysOQZGu1q4"
  }
}
```

### 1.3 获取当前用户信息

- **接口URL**: `/api/users/me`
- **请求方式**: GET
- **接口描述**: 获取当前登录用户的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 1.4 更新当前用户信息

- **接口URL**: `/api/users/me`
- **请求方式**: PUT
- **接口描述**: 更新当前登录用户的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **请求参数**:

```json
{
  "password": "newpassword123",
  "email": "<EMAIL>"
}
```

- **参数说明**:
  - `password`: 新密码，可选，长度6-20
  - `email`: 新邮箱，可选，但必须符合邮箱格式

- **响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 1.5 获取指定用户信息（需要VIP权限）

- **接口URL**: `/api/users/{id}`
- **请求方式**: GET
- **接口描述**: 根据用户ID获取用户详细信息，需要VIP权限
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 用户ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 2,
    "username": "anotheruser",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 1.6 删除用户（需要VIP权限）

- **接口URL**: `/api/users/{id}`
- **请求方式**: DELETE
- **接口描述**: 根据用户ID删除用户，需要VIP权限
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 用户ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

## 2. 文件管理接口

### 2.1 检查文件MD5

- **接口URL**: `/api/files/check-md5`
- **请求方式**: POST
- **接口描述**: 检查指定MD5的文件是否已存在，支持文件去重
- **请求头**:
  - `Authorization`: Bearer {token}
- **请求参数**:

```json
{
  "md5": "d41d8cd98f00b204e9800998ecf8427e",
  "originalName": "test.jpg",
  "size": 1024,
  "mimeType": "image/jpeg"
}
```

- **参数说明**:
  - `md5`: 文件MD5值，必填
  - `originalName`: 原始文件名，可选
  - `size`: 文件大小，可选
  - `mimeType`: 文件MIME类型，可选

- **响应示例（文件已存在）**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "exists": true,
    "fileResource": {
      "id": 1,
      "uuid": "abc123def456",
      "originalName": "test.jpg",
      "md5": "d41d8cd98f00b204e9800998ecf8427e",
      "size": 1024,
      "mimeType": "image/jpeg",
      "bucket": "images",
      "createTime": "2025-06-26T10:00:00",
      "accessUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
      "previewUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
      "downloadUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=..."
    },
    "message": "文件已存在，可直接使用"
  }
}
```

- **响应示例（文件不存在）**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "exists": false,
    "fileResource": null,
    "message": "文件不存在，需要上传"
  }
}
```

### 2.2 上传文件

- **接口URL**: `/api/files/upload`
- **请求方式**: POST
- **接口描述**: 上传文件到MinIO，支持MD5去重，自动选择存储桶
- **请求头**:
  - `Authorization`: Bearer {token}
  - `Content-Type`: multipart/form-data
- **请求参数**:
  - `file`: 要上传的文件（multipart/form-data）

- **存储桶分配规则**:
  - `image/*`: images桶
  - `audio/*`: audios桶
  - `text/*`: texts桶
  - `video/*`: videos桶
  - 其他: files桶

- **响应示例**:

```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "id": 1,
    "uuid": "abc123def456",
    "originalName": "test.jpg",
    "md5": "d41d8cd98f00b204e9800998ecf8427e",
    "size": 1024,
    "mimeType": "image/jpeg",
    "bucket": "images",
    "createTime": "2025-06-26T10:00:00",
    "accessUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
    "previewUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
    "downloadUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=..."
  }
}
```

### 2.3 获取文件信息

- **接口URL**: `/api/files/{uuid}`
- **请求方式**: GET
- **接口描述**: 根据UUID获取文件的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `uuid`: 文件UUID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "uuid": "abc123def456",
    "originalName": "test.jpg",
    "md5": "d41d8cd98f00b204e9800998ecf8427e",
    "size": 1024,
    "mimeType": "image/jpeg",
    "bucket": "images",
    "createTime": "2025-06-26T10:00:00",
    "accessUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
    "previewUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
    "downloadUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=..."
  }
}
```

### 2.4 获取文件下载URL

- **接口URL**: `/api/files/{uuid}/download`
- **请求方式**: GET
- **接口描述**: 获取文件的下载链接
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `uuid`: 文件UUID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "downloadUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=..."
  }
}
```

### 2.5 获取文件预览URL

- **接口URL**: `/api/files/{uuid}/preview`
- **请求方式**: GET
- **接口描述**: 获取文件的预览链接，仅支持图片和音频文件
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `uuid`: 文件UUID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "previewUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=..."
  }
}
```

### 2.6 获取文件列表

- **接口URL**: `/api/files`
- **请求方式**: GET
- **接口描述**: 分页获取文件列表，支持按存储桶和文件类型筛选
- **请求头**:
  - `Authorization`: Bearer {token}
- **查询参数**:
  - `bucket`: 存储桶名称（可选）
  - `mimeType`: MIME类型前缀，如 image、audio（可选）
  - `page`: 页码，从1开始（默认1）
  - `size`: 每页大小（默认10）

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "files": [
      {
        "id": 1,
        "uuid": "abc123def456",
        "originalName": "test.jpg",
        "md5": "d41d8cd98f00b204e9800998ecf8427e",
        "size": 1024,
        "mimeType": "image/jpeg",
        "bucket": "images",
        "createTime": "2025-06-26T10:00:00",
        "accessUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
        "previewUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
        "downloadUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=..."
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "totalPages": 1
  }
}
```

### 2.7 删除文件（需要VIP权限）

- **接口URL**: `/api/files/{uuid}`
- **请求方式**: DELETE
- **接口描述**: 删除指定UUID的文件，需要VIP权限
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `uuid`: 文件UUID

- **响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 3. 素材库管理接口

### 3.1 角色素材管理

#### 3.1.1 创建角色素材

- **接口URL**: `/api/roles`
- **请求方式**: POST
- **接口描述**: 创建新的角色素材，用于AI绘画参考
- **请求头**:
  - `Authorization`: Bearer {token}
- **请求参数**:

```json
{
  "name": "古风美女",
  "prompt": "beautiful ancient chinese woman, long black hair, traditional hanfu dress, elegant pose, detailed face, high quality",
  "imageUuid": "abc123def456",
  "tags": "古风,美女,汉服,黑发"
}
```

- **参数说明**:
  - `name`: 角色名称，必填，最大100字符
  - `prompt`: 角色Prompt描述，必填
  - `imageUuid`: 角色图片UUID，必填
  - `tags`: 角色标签，可选，多个标签用逗号分隔

- **响应示例**:

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "古风美女",
    "prompt": "beautiful ancient chinese woman, long black hair, traditional hanfu dress, elegant pose, detailed face, high quality",
    "imageUuid": "abc123def456",
    "imageUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
    "tags": "古风,美女,汉服,黑发",
    "tagList": ["古风", "美女", "汉服", "黑发"],
    "userId": 1,
    "createTime": "2025-06-26T10:00:00",
    "updateTime": "2025-06-26T10:00:00"
  }
}
```

#### 3.1.2 更新角色素材

- **接口URL**: `/api/roles/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定ID的角色素材
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 角色ID
- **请求参数**:

```json
{
  "name": "古风美女（更新）",
  "prompt": "updated prompt...",
  "imageUuid": "new123uuid456",
  "tags": "古风,美女,汉服,更新"
}
```

- **响应示例**: 同创建角色素材

#### 3.1.3 删除角色素材

- **接口URL**: `/api/roles/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定ID的角色素材
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 角色ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 3.1.4 获取角色素材详情

- **接口URL**: `/api/roles/{id}`
- **请求方式**: GET
- **接口描述**: 根据ID获取角色素材的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 角色ID

- **响应示例**: 同创建角色素材

#### 3.1.5 获取角色素材列表

- **接口URL**: `/api/roles`
- **请求方式**: GET
- **接口描述**: 分页获取当前用户的角色素材列表
- **请求头**:
  - `Authorization`: Bearer {token}
- **查询参数**:
  - `name`: 角色名称，支持模糊查询（可选）
  - `tags`: 标签，支持模糊查询（可选）
  - `page`: 页码，从1开始（默认1）
  - `size`: 每页大小（默认10）

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "roles": [
      {
        "id": 1,
        "name": "古风美女",
        "prompt": "beautiful ancient chinese woman...",
        "imageUuid": "abc123def456",
        "imageUrl": "http://minio:9000/images/abc123def456.jpg?X-Amz-Algorithm=...",
        "tags": "古风,美女,汉服,黑发",
        "tagList": ["古风", "美女", "汉服", "黑发"],
        "userId": 1,
        "createTime": "2025-06-26T10:00:00",
        "updateTime": "2025-06-26T10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "totalPages": 1
  }
}
```

#### 3.1.6 获取所有角色素材列表（VIP权限）

- **接口URL**: `/api/roles/admin/all`
- **请求方式**: GET
- **接口描述**: 分页获取所有用户的角色素材列表，需要VIP权限
- **请求头**:
  - `Authorization`: Bearer {token}
- **权限要求**: VIP用户
- **查询参数**: 同3.1.5
- **响应示例**: 同3.1.5

### 3.2 音频素材管理

#### 3.2.1 创建音频素材

- **接口URL**: `/api/audios`
- **请求方式**: POST
- **接口描述**: 创建新的音频素材，用于TTS参考
- **请求头**:
  - `Authorization`: Bearer {token}
- **请求参数**:

```json
{
  "name": "温柔女声",
  "description": "温柔甜美的女性声音，适合小说女主角",
  "audioUuid": "audio123uuid456",
  "duration": 30,
  "voiceType": "female_gentle"
}
```

- **参数说明**:
  - `name`: 音频名称，必填，最大100字符
  - `description`: 音频描述，可选，最大255字符
  - `audioUuid`: 音频文件UUID，必填
  - `duration`: 音频时长(秒)，必填，必须大于0
  - `voiceType`: 音色类型，可选，最大50字符

- **响应示例**:

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "温柔女声",
    "description": "温柔甜美的女性声音，适合小说女主角",
    "audioUuid": "audio123uuid456",
    "audioUrl": "http://minio:9000/audios/audio123uuid456.mp3?X-Amz-Algorithm=...",
    "duration": 30,
    "voiceType": "female_gentle",
    "userId": 1,
    "createTime": "2025-06-26T10:00:00"
  }
}
```

#### 3.2.2 更新音频素材

- **接口URL**: `/api/audios/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定ID的音频素材
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 音频ID
- **请求参数**:

```json
{
  "name": "温柔女声（更新）",
  "description": "更新后的描述",
  "audioUuid": "newaudio123uuid456",
  "duration": 35,
  "voiceType": "female_gentle_v2"
}
```

- **响应示例**: 同创建音频素材

#### 3.2.3 删除音频素材

- **接口URL**: `/api/audios/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定ID的音频素材
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 音频ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 3.2.4 获取音频素材详情

- **接口URL**: `/api/audios/{id}`
- **请求方式**: GET
- **接口描述**: 根据ID获取音频素材的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 音频ID

- **响应示例**: 同创建音频素材

#### 3.2.5 获取音频素材列表

- **接口URL**: `/api/audios`
- **请求方式**: GET
- **接口描述**: 分页获取当前用户的音频素材列表
- **请求头**:
  - `Authorization`: Bearer {token}
- **查询参数**:
  - `name`: 音频名称，支持模糊查询（可选）
  - `voiceType`: 音色类型（可选）
  - `page`: 页码，从1开始（默认1）
  - `size`: 每页大小（默认10）

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "audios": [
      {
        "id": 1,
        "name": "温柔女声",
        "description": "温柔甜美的女性声音，适合小说女主角",
        "audioUuid": "audio123uuid456",
        "audioUrl": "http://minio:9000/audios/audio123uuid456.mp3?X-Amz-Algorithm=...",
        "duration": 30,
        "voiceType": "female_gentle",
        "userId": 1,
        "createTime": "2025-06-26T10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "totalPages": 1
  }
}
```

#### 3.2.6 获取所有音频素材列表（VIP权限）

- **接口URL**: `/api/audios/admin/all`
- **请求方式**: GET
- **接口描述**: 分页获取所有用户的音频素材列表，需要VIP权限
- **请求头**:
  - `Authorization`: Bearer {token}
- **权限要求**: VIP用户
- **查询参数**: 同3.2.5
- **响应示例**: 同3.2.5

## 4. 小说管理接口

### 4.1 小说管理

#### 4.1.1 创建小说

- **接口URL**: `/api/novels`
- **请求方式**: POST
- **接口描述**: 创建新的小说
- **请求头**:
  - `Authorization`: Bearer {token}
- **请求参数**:

```json
{
  "title": "修仙传奇",
  "author": "网络作家",
  "coverUuid": "cover123uuid456",
  "description": "一个关于修仙的传奇故事...",
  "sourceUrl": "https://example.com/novel/123"
}
```

- **参数说明**:
  - `title`: 小说名称，必填，最大200字符
  - `author`: 作者，必填，最大100字符
  - `coverUuid`: 封面图片UUID，可选
  - `description`: 小说简介，可选
  - `sourceUrl`: 来源URL，可选，最大500字符

- **响应示例**:

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "title": "修仙传奇",
    "author": "网络作家",
    "coverUuid": "cover123uuid456",
    "coverUrl": "http://minio:9000/images/cover123uuid456.jpg?X-Amz-Algorithm=...",
    "description": "一个关于修仙的传奇故事...",
    "sourceUrl": "https://example.com/novel/123",
    "chapterCount": 0,
    "userId": 1,
    "createTime": "2025-06-26T10:00:00",
    "updateTime": "2025-06-26T10:00:00"
  }
}
```

#### 4.1.2 上传小说文件

- **接口URL**: `/api/novels/upload`
- **请求方式**: POST
- **接口描述**: 上传txt格式的小说文件，支持自动章节提取
- **请求头**:
  - `Authorization`: Bearer {token}
  - `Content-Type`: multipart/form-data
- **请求参数**:
  - `file`: 小说文件（txt格式），必填
  - `title`: 小说名称，必填
  - `author`: 作者，必填
  - `coverUuid`: 封面图片UUID，可选
  - `description`: 小说简介，可选
  - `sourceUrl`: 来源URL，可选
  - `autoExtractChapters`: 是否自动提取章节，默认true

- **响应示例**: 同创建小说

#### 4.1.3 更新小说

- **接口URL**: `/api/novels/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定ID的小说信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 小说ID
- **请求参数**:

```json
{
  "title": "修仙传奇（修订版）",
  "author": "网络作家",
  "coverUuid": "newcover123uuid456",
  "description": "更新后的小说简介...",
  "sourceUrl": "https://example.com/novel/123-updated"
}
```

- **响应示例**: 同创建小说

#### 4.1.4 删除小说

- **接口URL**: `/api/novels/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定ID的小说及其所有章节
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 小说ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 4.1.5 获取小说详情

- **接口URL**: `/api/novels/{id}`
- **请求方式**: GET
- **接口描述**: 根据ID获取小说的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 小说ID

- **响应示例**: 同创建小说

#### 4.1.6 获取小说列表

- **接口URL**: `/api/novels`
- **请求方式**: GET
- **接口描述**: 分页获取当前用户的小说列表
- **请求头**:
  - `Authorization`: Bearer {token}
- **查询参数**:
  - `title`: 小说标题，支持模糊查询（可选）
  - `author`: 作者，支持模糊查询（可选）
  - `page`: 页码，从1开始（默认1）
  - `size`: 每页大小（默认10）

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "novels": [
      {
        "id": 1,
        "title": "修仙传奇",
        "author": "网络作家",
        "coverUuid": "cover123uuid456",
        "coverUrl": "http://minio:9000/images/cover123uuid456.jpg?X-Amz-Algorithm=...",
        "description": "一个关于修仙的传奇故事...",
        "sourceUrl": "https://example.com/novel/123",
        "chapterCount": 50,
        "userId": 1,
        "createTime": "2025-06-26T10:00:00",
        "updateTime": "2025-06-26T10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "totalPages": 1
  }
}
```

#### 4.1.7 获取所有小说列表（VIP权限）

- **接口URL**: `/api/novels/admin/all`
- **请求方式**: GET
- **接口描述**: 分页获取所有用户的小说列表，需要VIP权限
- **请求头**:
  - `Authorization`: Bearer {token}
- **权限要求**: VIP用户
- **查询参数**: 同4.1.6
- **响应示例**: 同4.1.6

### 4.2 小说章节管理

#### 4.2.1 上传单个章节文件

- **接口URL**: `/api/novels/{novelId}/chapters/upload`
- **请求方式**: POST
- **接口描述**: 上传单个txt格式的章节文件，智能解析文件名获取章节信息
- **请求头**:
  - `Authorization`: Bearer {token}
  - `Content-Type`: multipart/form-data
- **路径参数**:
  - `novelId`: 小说ID
- **请求参数**:
  - `file`: 章节文件（txt格式），必填
  - `title`: 章节标题（可选，如果不提供则从文件名解析）
  - `chapterIndex`: 章节序号（可选，如果不提供则从文件名解析）
  - `overwrite`: 是否覆盖已存在的章节，默认false

- **文件名解析支持格式**:
  - `第一章 初入修仙界.txt`
  - `第1章 初入修仙界.txt`
  - `第二十三章 突破境界.txt`
  - `第23章 突破境界.txt`
  - `Chapter 1 The Beginning.txt`
  - `1. 序章.txt`
  - `1 序章.txt`
  - `1-序章.txt`
  - `1_序章.txt`

- **响应示例**:

```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "id": 1,
    "novelId": 1,
    "title": "第一章 初入修仙界",
    "chapterIndex": 1,
    "contentUuid": "chapter123uuid456",
    "wordCount": 3000,
    "isSceneParsed": false,
    "isCharacterParsed": false,
    "createTime": "2025-06-26T10:00:00"
  }
}
```

#### 4.2.2 批量上传章节文件

- **接口URL**: `/api/novels/{novelId}/chapters/batch-upload`
- **请求方式**: POST
- **接口描述**: 批量上传多个txt格式的章节文件
- **请求头**:
  - `Authorization`: Bearer {token}
  - `Content-Type`: multipart/form-data
- **路径参数**:
  - `novelId`: 小说ID
- **请求参数**:
  - `files`: 章节文件列表（txt格式），必填
  - `overwrite`: 是否覆盖已存在的章节，默认false

- **响应示例**:

```json
{
  "code": 200,
  "message": "批量上传完成",
  "data": {
    "successChapters": [
      {
        "id": 1,
        "novelId": 1,
        "title": "第一章 初入修仙界",
        "chapterIndex": 1,
        "contentUuid": "chapter123uuid456",
        "wordCount": 3000,
        "isSceneParsed": false,
        "isCharacterParsed": false,
        "createTime": "2025-06-26T10:00:00"
      }
    ],
    "failedUploads": [
      {
        "fileName": "invalid.txt",
        "reason": "文件内容为空",
        "errorCode": "EMPTY_FILE"
      }
    ],
    "totalFiles": 2,
    "successCount": 1,
    "failedCount": 1
  }
}
```

#### 4.2.3 手动创建章节

- **接口URL**: `/api/novels/{novelId}/chapters/manual`
- **请求方式**: POST
- **接口描述**: 通过文本内容手动创建章节
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
- **请求参数**:
  - `title`: 章节标题，必填
  - `content`: 章节内容，必填

- **响应示例**: 同4.2.1

#### 4.2.4 手动更新章节

- **接口URL**: `/api/novels/{novelId}/chapters/{chapterId}/manual`
- **请求方式**: PUT
- **接口描述**: 通过文本内容更新指定ID的章节
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `chapterId`: 章节ID
- **请求参数**:
  - `title`: 章节标题，必填
  - `content`: 章节内容，必填

- **响应示例**: 同4.2.1

#### 4.2.5 上传文件更新章节

- **接口URL**: `/api/novels/{novelId}/chapters/{chapterId}/upload`
- **请求方式**: PUT
- **接口描述**: 通过上传txt文件更新指定ID的章节
- **请求头**:
  - `Authorization`: Bearer {token}
  - `Content-Type`: multipart/form-data
- **路径参数**:
  - `novelId`: 小说ID
  - `chapterId`: 章节ID
- **请求参数**:
  - `file`: 章节文件（txt格式），必填
  - `title`: 章节标题（可选，如果不提供则从文件名解析）

- **响应示例**: 同4.2.1

#### 4.2.6 删除章节

- **接口URL**: `/api/novels/{novelId}/chapters/{chapterId}`
- **请求方式**: DELETE
- **接口描述**: 删除指定ID的章节
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `chapterId`: 章节ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 4.2.7 获取章节详情

- **接口URL**: `/api/novels/{novelId}/chapters/{chapterId}`
- **请求方式**: GET
- **接口描述**: 根据ID获取章节的详细信息，包含内容
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `chapterId`: 章节ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "novelId": 1,
    "title": "第一章 初入修仙界",
    "chapterIndex": 1,
    "contentUuid": "chapter123uuid456",
    "content": "章节的完整文本内容...",
    "wordCount": 3000,
    "isSceneParsed": false,
    "isCharacterParsed": false,
    "createTime": "2025-06-26T10:00:00"
  }
}
```

#### 4.2.8 获取章节列表

- **接口URL**: `/api/novels/{novelId}/chapters`
- **请求方式**: GET
- **接口描述**: 分页获取指定小说的章节列表
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
- **查询参数**:
  - `page`: 页码，从1开始（默认1）
  - `size`: 每页大小（默认10）

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "chapters": [
      {
        "id": 1,
        "novelId": 1,
        "title": "第一章 初入修仙界",
        "chapterIndex": 1,
        "contentUuid": "chapter123uuid456",
        "wordCount": 3000,
        "isSceneParsed": false,
        "isCharacterParsed": false,
        "createTime": "2025-06-26T10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "totalPages": 1
  }
}
```

#### 4.2.9 获取章节内容

- **接口URL**: `/api/novels/{novelId}/chapters/{chapterId}/content`
- **请求方式**: GET
- **接口描述**: 获取指定章节的文本内容
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `chapterId`: 章节ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "content": "章节的完整文本内容..."
  }
}
```

#### 4.2.10 更新分镜解析状态

- **接口URL**: `/api/novels/{novelId}/chapters/{chapterId}/scene-parsed`
- **请求方式**: PUT
- **接口描述**: 更新章节的分镜解析状态
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `chapterId`: 章节ID
- **请求参数**:
  - `isSceneParsed`: 是否已分镜解析，必填

- **响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

#### 4.2.11 更新角色解析状态

- **接口URL**: `/api/novels/{novelId}/chapters/{chapterId}/character-parsed`
- **请求方式**: PUT
- **接口描述**: 更新章节的角色解析状态
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `chapterId`: 章节ID
- **请求参数**:
  - `isCharacterParsed`: 是否已角色解析，必填

- **响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

## 5. 小说角色管理接口

### 5.1 AI角色解析

#### 5.1.1 AI异步解析章节角色

- **接口URL**: `/api/novels/{novelId}/characters/parse`
- **请求方式**: POST
- **接口描述**: 使用AI异步解析指定章节中的角色并创建角色记录
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
- **请求参数**:

```json
{
  "novelId": 1,
  "chapterIds": [1, 2, 3],
  "overwrite": false,
  "autoLinkRole": false
}
```

- **参数说明**:
  - `novelId`: 小说ID，必填
  - `chapterIds`: 要解析的章节ID列表，必填
  - `overwrite`: 是否覆盖已存在的角色，默认false
  - `autoLinkRole`: 是否自动关联角色素材，默认true

- **响应示例**:

```json
{
  "code": 200,
  "message": "解析任务已启动",
  "data": {
    "taskId": "character_parse_1703587200000_1",
    "message": "角色解析任务已启动，请使用taskId查询进度"
  }
}
```

#### 5.1.2 查询解析任务状态

- **接口URL**: `/api/novels/{novelId}/characters/parse/status/{taskId}`
- **请求方式**: GET
- **接口描述**: 查询角色解析任务的执行状态和结果
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `taskId`: 任务ID

- **响应示例**:

**进行中的任务**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "taskId": "character_parse_1703587200000_1",
    "status": "RUNNING",
    "description": "角色解析任务 - 小说ID: 1",
    "progress": 45,
    "currentStep": "解析章节: 第二章 修炼之路",
    "result": null,
    "errorMessage": null,
    "startTime": "2025-06-26T10:00:00",
    "endTime": null,
    "duration": null
  }
}
```

**已完成的任务**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "taskId": "character_parse_1703587200000_1",
    "status": "COMPLETED",
    "description": "角色解析任务 - 小说ID: 1",
    "progress": 100,
    "currentStep": null,
    "result": {
      "parsedCharacters": [
        {
          "id": 1,
          "novelId": 1,
          "name": "李白",
          "description": "主角，年轻的修仙者",
          "roleId": 1,
          "roleInfo": {
            "id": 1,
            "name": "古风男主",
            "imageUrl": "http://minio:9000/images/role1.jpg"
          },
          "audioId": null,
          "audioInfo": null,
          "createTime": "2025-06-26T10:00:00",
          "updateTime": "2025-06-26T10:00:00"
        }
      ],
      "failedChapters": [],
      "totalChapters": 3,
      "successCount": 3,
      "failedCount": 0,
      "totalCharacters": 1
    },
    "errorMessage": null,
    "startTime": "2025-06-26T10:00:00",
    "endTime": "2025-06-26T10:02:30",
    "duration": 150000
  }
}
```

### 5.2 角色管理

#### 5.2.1 手动创建角色

- **接口URL**: `/api/novels/{novelId}/characters`
- **请求方式**: POST
- **接口描述**: 手动创建小说角色
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
- **请求参数**:
  - `name`: 角色名称，必填
  - `description`: 角色描述，可选

- **响应示例**:

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "novelId": 1,
    "name": "李白",
    "description": "主角，年轻的修仙者",
    "roleId": null,
    "roleInfo": null,
    "audioId": null,
    "audioInfo": null,
    "createTime": "2025-06-26T10:00:00",
    "updateTime": "2025-06-26T10:00:00"
  }
}
```

#### 5.2.2 更新角色

- **接口URL**: `/api/novels/{novelId}/characters/{characterId}`
- **请求方式**: PUT
- **接口描述**: 更新指定ID的角色信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `characterId`: 角色ID
- **请求参数**:
  - `name`: 角色名称，可选
  - `description`: 角色描述，可选

- **响应示例**: 同创建角色

#### 5.2.3 关联角色素材

- **接口URL**: `/api/novels/{novelId}/characters/{characterId}/link-role`
- **请求方式**: PUT
- **接口描述**: 为角色关联角色素材
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `characterId`: 角色ID
- **请求参数**:
  - `roleId`: 角色素材ID，必填

- **响应示例**: 同创建角色（包含roleInfo信息）

#### 5.2.4 关联音频素材

- **接口URL**: `/api/novels/{novelId}/characters/{characterId}/link-audio`
- **请求方式**: PUT
- **接口描述**: 为角色关联音频素材
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `characterId`: 角色ID
- **请求参数**:
  - `audioId`: 音频素材ID，必填

- **响应示例**: 同创建角色（包含audioInfo信息）

#### 5.2.5 自动关联角色素材(没用接口)

- **接口URL**: `/api/novels/{novelId}/characters/{characterId}/auto-link`
- **请求方式**: PUT
- **接口描述**: 根据角色名称自动关联角色素材
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `characterId`: 角色ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "自动关联成功",
  "data": null
}
```

#### 5.2.6 删除角色

- **接口URL**: `/api/novels/{novelId}/characters/{characterId}`
- **请求方式**: DELETE
- **接口描述**: 删除指定ID的角色
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `characterId`: 角色ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 5.2.7 获取角色详情

- **接口URL**: `/api/novels/{novelId}/characters/{characterId}`
- **请求方式**: GET
- **接口描述**: 根据ID获取角色的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
  - `characterId`: 角色ID

- **响应示例**: 同创建角色

#### 5.2.8 获取角色列表

- **接口URL**: `/api/novels/{novelId}/characters`
- **请求方式**: GET
- **接口描述**: 分页获取指定小说的角色列表
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
- **查询参数**:
  - `page`: 页码，从1开始（默认1）
  - `size`: 每页大小（默认10）

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "characters": [
      {
        "id": 1,
        "novelId": 1,
        "name": "李白",
        "description": "主角，年轻的修仙者",
        "roleId": 1,
        "roleInfo": {
          "id": 1,
          "name": "古风男主",
          "imageUrl": "http://minio:9000/images/role1.jpg"
        },
        "audioId": 1,
        "audioInfo": {
          "id": 1,
          "name": "温柔男声",
          "audioUrl": "http://minio:9000/audios/audio1.mp3"
        },
        "createTime": "2025-06-26T10:00:00",
        "updateTime": "2025-06-26T10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "totalPages": 1
  }
}
```

## 6. AI配置说明

### 6.1 配置文件结构

本系统使用多个YAML配置文件来管理不同类型的配置：

- `application.yml` - 主配置文件
- `application-ai.yml` - AI模型配置
- `application-prompts.yml` - AI提示词配置

### 6.2 AI模型配置 (application-ai.yml)

```yaml
ai:
  # Gemini配置
  gemini:
    api-key: AXXXXXXX
    base-url: https://generativelanguage.googleapis.com/v1beta
    model: gemini-2.0-flash
    temperature: 0.3
    max-output-tokens: 2048
    timeout: 30s
    retry:
      max-attempts: 3
      initial-delay: 2s
      multiplier: 2
      max-delay: 30s

  # Ollama配置
  ollama:
    base-url: http://localhost:11434
    model: llama3.2
    temperature: 0.7
    timeout: 60s
    retry:
      max-attempts: 2
      initial-delay: 1s
      multiplier: 1.5
      max-delay: 10s

  # 默认使用的模型类型
  default-provider: gemini  # gemini 或 ollama
```

### 6.3 环境变量配置

可以通过环境变量覆盖配置：

- `GEMINI_API_KEY` - Gemini API密钥
- `OLLAMA_BASE_URL` - Ollama服务地址
- `OLLAMA_MODEL` - Ollama模型名称

### 6.4 AI服务使用说明

#### Gemini API
- 用于复杂的文本分析任务（如角色解析）
- 需要有效的API密钥
- 支持自动重试机制

#### Ollama API
- 用于简单的问答任务
- 需要本地部署Ollama服务
- 支持多种开源模型

## 7. 异步任务管理接口

### 7.1 任务状态查询

#### 7.1.1 获取任务状态

- **接口URL**: `/api/tasks/{taskId}`
- **请求方式**: GET
- **接口描述**: 根据任务ID获取任务的执行状态和结果
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `taskId`: 任务ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "taskId": "character_parse_1703587200000_1",
    "status": "COMPLETED",
    "description": "角色解析任务 - 小说ID: 1",
    "progress": 100,
    "currentStep": null,
    "result": {
      "parsedCharacters": [...],
      "totalCharacters": 5
    },
    "errorMessage": null,
    "startTime": "2025-06-26T10:00:00",
    "endTime": "2025-06-26T10:02:30",
    "duration": 150000
  }
}
```

#### 7.1.2 获取用户任务列表

- **接口URL**: `/api/tasks`
- **请求方式**: GET
- **接口描述**: 分页获取当前用户的任务列表
- **请求头**:
  - `Authorization`: Bearer {token}
- **查询参数**:
  - `taskType`: 任务类型（可选）
  - `status`: 任务状态（可选）
  - `page`: 页码，从1开始（默认1）
  - `size`: 每页大小（默认10）

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "tasks": [
      {
        "id": 1,
        "taskId": "character_parse_1703587200000_1",
        "taskType": "CHARACTER_PARSE",
        "taskName": "角色解析任务 - 小说ID: 1",
        "status": "COMPLETED",
        "progress": 100,
        "createTime": "2025-06-26T10:00:00",
        "endTime": "2025-06-26T10:02:30",
        "duration": 150000
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "totalPages": 1
  }
}
```

#### 7.1.3 获取任务统计信息

- **接口URL**: `/api/tasks/statistics`
- **请求方式**: GET
- **接口描述**: 获取当前用户的任务统计信息
- **请求头**:
  - `Authorization`: Bearer {token}

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "pending": 2,
    "running": 1,
    "completed": 15,
    "failed": 1,
    "byType": {
      "CHARACTER_PARSE": 10,
      "SCENE_PARSE": 5,
      "VIDEO_GENERATE": 4
    },
    "total": 19
  }
}
```

## 8. 章节分镜管理接口

### 8.1 分镜解析

#### 8.1.1 AI异步解析章节分镜

- **接口URL**: `/api/novels/{novelId}/scenes/parse`
- **请求方式**: POST
- **接口描述**: 使用AI异步解析指定章节的分镜并创建分镜记录
- **处理流程**:
  1. **内容简化**: 首先使用 `simplify-prompt` 对原始章节内容进行简化处理，压缩至约1/3篇幅（使用代理）
  2. **分镜解析**: 使用简化后的内容和 `scene-parse` prompt 进行AI分镜解析，生成结构化的漫画分镜片段（使用代理）
- **技术细节**:
  - 两个步骤都通过 `AIClientService.callGemini()` 调用，自动使用配置的代理设置
  - **严格模式**：如果简化步骤失败，整个任务将直接失败，不会回退到原始内容
  - 所有AI调用都包含重试机制和详细的日志记录
  - 超时配置已优化为120秒，适合处理长文本内容
  - **AI响应处理**：支持markdown格式的JSON代码块（```json...```）
  - **角色自动创建**：AI解析出的角色如果不存在会自动创建，描述标注为"AI解析漏掉的角色"
  - **数据结构变更**：chapter_scene表的character_id字段已改为characters字段，存储JSON格式的角色名称列表
  - **性能优化**：批量处理角色创建，先收集整个章节的所有角色去重，避免重复数据库查询
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `novelId`: 小说ID
- **请求参数**:

```json
{
  "chapterIds": [1, 2, 3],
  "overwrite": false,
  "autoLinkCharacter": true,
  "generatePrompt": true
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "解析任务已启动",
  "data": {
    "taskId": "scene_parse_1703587200000_1",
    "message": "分镜解析任务已启动，请使用taskId查询进度",
    "duration": "25ms"
  }
}
```

**AI分镜解析结果示例：**
```json
[
  {
    "content": "好！吃了这孙长老点名要入汤的百年灵芝，咱们就是自己人了。",
    "speaker": "张大胖",
    "characters": ["张大胖", "白小纯"],
    "prompt": "A misty courtyard at night. The left character (Zhang Dapang) is smiling with great satisfaction, sharing pieces of the spirit ganoderma. The right character (Bai Xiaochun) looks thoughtful, understanding he has become complicit. Traditional Chinese fantasy style."
  },
  {
    "content": "白小纯这才明白这是同流合污。",
    "speaker": "Narrator",
    "characters": ["白小纯"],
    "prompt": "Close-up of the character with a dawning realization expression. Traditional Chinese fantasy style with misty background."
  }
]
```

#### 8.1.2 查询分镜解析任务状态

- **接口URL**: `/api/novels/{novelId}/scenes/parse/status/{taskId}`
- **请求方式**: GET
- **接口描述**: 查询分镜解析任务的执行状态和结果

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "taskId": "scene_parse_1703587200000_1",
    "status": "COMPLETED",
    "result": {
      "parsedScenes": [
        {
          "id": 1,
          "chapterId": 1,
          "sceneIndex": 1,
          "content": "李白站在山峰之上，望着远方的云海。",
          "speaker": "李白",
          "prompt": "A young man standing on a mountain peak, ancient Chinese style",
          "characterId": 1
        }
      ],
      "totalScenes": 15,
      "successCount": 3
    }
  }
}
```

### 8.2 分镜管理

#### 8.2.1 获取章节分镜列表

- **接口URL**: `/api/novels/{novelId}/scenes/chapters/{chapterId}`
- **请求方式**: GET
- **接口描述**: 获取指定章节的所有分镜

#### 8.2.2 自动关联角色

- **接口URL**: `/api/novels/{novelId}/scenes/chapters/{chapterId}/auto-link`
- **请求方式**: POST
- **接口描述**: 自动关联章节分镜中的说话者到角色

- **响应示例**:

```json
{
  "code": 200,
  "message": "关联完成",
  "data": {
    "linkedCount": 8,
    "message": "自动关联完成，共关联 8 个分镜"
  }
}
```
